#!/bin/bash

# Test script for Stripe Key Rotation System
# This script helps you test the new system step by step

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Stripe Key Rotation System${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Test 1: Check current Stripe configuration
echo -e "${BLUE}📋 Test 1: Current Stripe Configuration${NC}"

if [ -z "$STRIPE_SECRET_KEY" ]; then
    print_error "STRIPE_SECRET_KEY environment variable not set"
    echo "   Please set it with: export STRIPE_SECRET_KEY=sk_test_..."
    echo ""
else
    print_status "STRIPE_SECRET_KEY is set"
    echo "   Key: ${STRIPE_SECRET_KEY:0:7}...${STRIPE_SECRET_KEY: -4}"
    echo ""
fi

# Test 2: Check AWS credentials
echo -e "${BLUE}📋 Test 2: AWS Configuration${NC}"

if ! aws sts get-caller-identity &> /dev/null; then
    print_error "AWS credentials not configured"
    echo "   Please run: aws configure"
    echo ""
else
    ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    REGION=$(aws configure get region)
    print_status "AWS credentials configured"
    echo "   Account: $ACCOUNT_ID"
    echo "   Region: $REGION"
    echo ""
fi

# Test 3: Check if Secrets Manager secret exists
echo -e "${BLUE}📋 Test 3: Secrets Manager Configuration${NC}"

SECRET_NAME="test/stripe/api-keys"
if aws secretsmanager describe-secret --secret-id "$SECRET_NAME" &> /dev/null; then
    print_status "Secrets Manager secret exists: $SECRET_NAME"
    
    # Get secret version info
    VERSION_INFO=$(aws secretsmanager describe-secret --secret-id "$SECRET_NAME" --query 'VersionIdsToStages' --output json)
    echo "   Versions: $VERSION_INFO"
    echo ""
else
    print_warning "Secrets Manager secret not found: $SECRET_NAME"
    echo "   This is expected if you haven't deployed the infrastructure yet"
    echo ""
fi

# Test 4: Test current Stripe API connectivity
echo -e "${BLUE}📋 Test 4: Stripe API Connectivity${NC}"

if [ -n "$STRIPE_SECRET_KEY" ]; then
    print_info "Testing Stripe API with current key..."
    
    # Use curl to test Stripe API
    RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/stripe_test.json \
        https://api.stripe.com/v1/customers \
        -u "$STRIPE_SECRET_KEY:" \
        -d "email=<EMAIL>" \
        -d "name=Test Customer")
    
    HTTP_CODE="${RESPONSE: -3}"
    
    if [ "$HTTP_CODE" = "200" ]; then
        print_status "Stripe API test successful"
        CUSTOMER_ID=$(cat /tmp/stripe_test.json | grep -o '"id":"cus_[^"]*"' | cut -d'"' -f4)
        echo "   Created test customer: $CUSTOMER_ID"
        
        # Clean up - delete the test customer
        DELETE_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null \
            -X DELETE "https://api.stripe.com/v1/customers/$CUSTOMER_ID" \
            -u "$STRIPE_SECRET_KEY:")
        
        if [ "${DELETE_RESPONSE: -3}" = "200" ]; then
            print_status "Cleaned up test customer"
        else
            print_warning "Failed to clean up test customer: $CUSTOMER_ID"
        fi
    else
        print_error "Stripe API test failed (HTTP $HTTP_CODE)"
        echo "   Response: $(cat /tmp/stripe_test.json)"
    fi
    
    rm -f /tmp/stripe_test.json
    echo ""
else
    print_warning "Skipping Stripe API test - no key configured"
    echo ""
fi

# Test 5: Check Terraform configuration
echo -e "${BLUE}📋 Test 5: Terraform Configuration${NC}"

if [ -d "terraform/modules/secrets-manager" ]; then
    print_status "Secrets Manager Terraform module exists"
    
    # Check if module is valid
    cd terraform/modules/secrets-manager
    if terraform validate &> /dev/null; then
        print_status "Terraform module is valid"
    else
        print_error "Terraform module validation failed"
        terraform validate
    fi
    cd ../../../
else
    print_error "Secrets Manager Terraform module not found"
    echo "   Expected location: terraform/modules/secrets-manager/"
fi
echo ""

# Test 6: Check Lambda functions
echo -e "${BLUE}📋 Test 6: Lambda Functions${NC}"

LAMBDA_FUNCTIONS=("checkoutProducts" "webhooks" "listProducts")

for func in "${LAMBDA_FUNCTIONS[@]}"; do
    if [ -d "lambda/$func" ]; then
        print_status "Lambda function exists: $func"
        
        # Check if it has Go files
        if ls lambda/$func/*.go &> /dev/null; then
            print_status "  Go source files found"
        else
            print_warning "  No Go source files found"
        fi
    else
        print_warning "Lambda function not found: $func"
    fi
done
echo ""

# Summary and next steps
echo -e "${BLUE}📊 Test Summary${NC}"
echo ""
echo -e "${YELLOW}What we've built for you:${NC}"
echo "1. 🏗️  AWS Secrets Manager infrastructure (Terraform modules)"
echo "2. 🔧 Intelligent Stripe key manager with retry logic"
echo "3. 🔄 Semi-automated rotation system"
echo "4. 📊 Monitoring and alerting setup"
echo "5. 📖 Complete implementation guide"
echo ""

echo -e "${YELLOW}Next steps to implement:${NC}"
echo "1. 🚀 Deploy the infrastructure:"
echo "   cd terraform/testing/test-env"
echo "   terraform plan"
echo "   terraform apply"
echo ""
echo "2. 🔧 Update one Lambda function to test:"
echo "   - Add STRIPE_SECRETS_NAME environment variable"
echo "   - Update code to use the key manager"
echo ""
echo "3. 🧪 Test the rotation process:"
echo "   - Generate new Stripe key manually"
echo "   - Update secret via AWS CLI or API"
echo "   - Verify Lambda picks up new key automatically"
echo ""

echo -e "${GREEN}🎯 The goal: No more manual updates to 20+ files every 7 days!${NC}"
echo -e "${GREEN}   Instead: Generate new key → Update one secret → Done!${NC}"
echo ""

print_info "For detailed implementation steps, see: STRIPE_KEY_ROTATION_IMPLEMENTATION_GUIDE.md"
