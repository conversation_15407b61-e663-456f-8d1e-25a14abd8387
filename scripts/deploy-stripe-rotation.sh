#!/bin/bash

# Stripe Key Rotation Deployment Script
# This script helps deploy the production-grade Stripe key rotation solution

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-test}
AWS_REGION=${2:-us-west-2}
LAMBDA_BUCKET="admin-thealpinestudio-lambda-functions"

echo -e "${BLUE}🚀 Deploying Stripe Key Rotation Solution for ${ENVIRONMENT} environment${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check prerequisites
echo -e "${BLUE}📋 Checking prerequisites...${NC}"

if ! command -v aws &> /dev/null; then
    print_error "AWS CLI not found. Please install AWS CLI."
    exit 1
fi

if ! command -v terraform &> /dev/null; then
    print_error "Terraform not found. Please install Terraform."
    exit 1
fi

if ! command -v go &> /dev/null; then
    print_error "Go not found. Please install Go."
    exit 1
fi

print_status "Prerequisites check passed"

# Step 1: Build and deploy rotation Lambda
echo -e "${BLUE}🔨 Building Stripe Key Rotation Lambda...${NC}"

cd lambda/stripeKeyRotation

# Build for Linux
GOOS=linux GOARCH=amd64 go build -o bootstrap main.go
if [ $? -ne 0 ]; then
    print_error "Failed to build rotation Lambda"
    exit 1
fi

# Create deployment package
zip stripe-key-rotation.zip bootstrap
if [ $? -ne 0 ]; then
    print_error "Failed to create deployment package"
    exit 1
fi

# Upload to S3
aws s3 cp stripe-key-rotation.zip s3://${LAMBDA_BUCKET}/stripe-key-rotation.zip --region ${AWS_REGION}
if [ $? -ne 0 ]; then
    print_error "Failed to upload Lambda to S3"
    exit 1
fi

print_status "Rotation Lambda built and uploaded"

# Clean up
rm bootstrap stripe-key-rotation.zip

cd ../../

# Step 2: Update Terraform configuration
echo -e "${BLUE}🏗️  Updating Terraform configuration...${NC}"

# Check if secrets manager module exists
if [ ! -d "terraform/modules/secrets-manager" ]; then
    print_error "Secrets Manager module not found. Please ensure the module is created."
    exit 1
fi

# Navigate to environment directory
cd terraform/testing/test-env

# Check if terraform is initialized
if [ ! -d ".terraform" ]; then
    print_warning "Terraform not initialized. Running terraform init..."
    terraform init
fi

# Validate configuration
terraform validate
if [ $? -ne 0 ]; then
    print_error "Terraform validation failed"
    exit 1
fi

print_status "Terraform configuration validated"

# Step 3: Plan deployment
echo -e "${BLUE}📋 Planning Terraform deployment...${NC}"

terraform plan -var-file=terraform.tfvars -out=tfplan
if [ $? -ne 0 ]; then
    print_error "Terraform plan failed"
    exit 1
fi

print_status "Terraform plan completed"

# Step 4: Ask for confirmation
echo -e "${YELLOW}⚠️  Ready to deploy Stripe key rotation infrastructure.${NC}"
echo -e "${YELLOW}This will:${NC}"
echo -e "${YELLOW}- Create AWS Secrets Manager secret for Stripe keys${NC}"
echo -e "${YELLOW}- Set up monitoring and alerting${NC}"
echo -e "${YELLOW}- Deploy rotation Lambda function${NC}"
echo -e "${YELLOW}- Update IAM permissions${NC}"
echo ""
read -p "Do you want to proceed? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    exit 0
fi

# Step 5: Apply Terraform
echo -e "${BLUE}🚀 Applying Terraform configuration...${NC}"

terraform apply tfplan
if [ $? -ne 0 ]; then
    print_error "Terraform apply failed"
    exit 1
fi

print_status "Infrastructure deployed successfully"

# Step 6: Get outputs
echo -e "${BLUE}📊 Getting deployment outputs...${NC}"

SECRET_ARN=$(terraform output -raw stripe_secret_arn 2>/dev/null || echo "Not available")
SECRET_NAME=$(terraform output -raw stripe_secret_name 2>/dev/null || echo "Not available")

# Step 7: Provide next steps
echo -e "${GREEN}🎉 Stripe Key Rotation Solution Deployed Successfully!${NC}"
echo ""
echo -e "${BLUE}📋 Next Steps:${NC}"
echo "1. Update your Lambda functions to use the new secret:"
echo "   - Set environment variable: STRIPE_SECRETS_NAME=${SECRET_NAME}"
echo "   - Update code to use StripeKeyManager"
echo ""
echo "2. Test the rotation process:"
echo "   - Generate a new Stripe API key in the dashboard"
echo "   - Call the rotation API with the new key"
echo ""
echo "3. Set up email notifications:"
echo "   - Subscribe to the SNS topic for rotation alerts"
echo ""
echo -e "${BLUE}📝 Important Information:${NC}"
echo "Secret ARN: ${SECRET_ARN}"
echo "Secret Name: ${SECRET_NAME}"
echo "Environment: ${ENVIRONMENT}"
echo "Region: ${AWS_REGION}"
echo ""
echo -e "${YELLOW}⚠️  Remember:${NC}"
echo "- Stripe keys still need to be manually generated (2FA requirement)"
echo "- The system will notify you when rotation is needed"
echo "- Test the rotation process in this environment first"
echo ""
echo -e "${GREEN}✅ Deployment completed successfully!${NC}"

# Clean up
rm -f tfplan

cd ../../../

echo -e "${BLUE}📖 For detailed implementation guide, see: STRIPE_KEY_ROTATION_IMPLEMENTATION_GUIDE.md${NC}"
