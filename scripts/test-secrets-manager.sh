#!/bin/bash

# Test script for Stripe Secrets Manager
# This script demonstrates how the new system works

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Stripe Secrets Manager${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -d "terraform/testing/test-secrets-manager" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

cd terraform/testing/test-secrets-manager

# Step 1: Check if terraform.tfvars exists
echo -e "${BLUE}📋 Step 1: Configuration Check${NC}"

if [ ! -f "terraform.tfvars" ]; then
    print_warning "terraform.tfvars not found"
    echo "   Creating from example..."
    cp terraform.tfvars.example terraform.tfvars
    print_info "Please edit terraform.tfvars with your actual Stripe keys"
    echo "   Then run this script again"
    exit 0
else
    print_status "terraform.tfvars found"
fi

# Step 2: Initialize Terraform
echo -e "${BLUE}🏗️  Step 2: Initialize Terraform${NC}"

if [ ! -d ".terraform" ]; then
    print_info "Initializing Terraform..."
    terraform init
    if [ $? -eq 0 ]; then
        print_status "Terraform initialized"
    else
        print_error "Terraform initialization failed"
        exit 1
    fi
else
    print_status "Terraform already initialized"
fi

# Step 3: Validate configuration
echo -e "${BLUE}✅ Step 3: Validate Configuration${NC}"

terraform validate
if [ $? -eq 0 ]; then
    print_status "Terraform configuration is valid"
else
    print_error "Terraform validation failed"
    exit 1
fi

# Step 4: Plan deployment
echo -e "${BLUE}📋 Step 4: Plan Deployment${NC}"

print_info "Running terraform plan..."
terraform plan -out=tfplan
if [ $? -eq 0 ]; then
    print_status "Terraform plan completed successfully"
else
    print_error "Terraform plan failed"
    exit 1
fi

# Step 5: Ask for confirmation
echo ""
echo -e "${YELLOW}⚠️  Ready to deploy Stripe Secrets Manager${NC}"
echo -e "${YELLOW}This will create:${NC}"
echo "   • AWS Secrets Manager secret for Stripe keys"
echo "   • IAM policy for Lambda access"
echo "   • CloudWatch monitoring and alarms"
echo "   • SNS topic for notifications (optional)"
echo ""
read -p "Do you want to proceed with deployment? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_warning "Deployment cancelled"
    rm -f tfplan
    exit 0
fi

# Step 6: Apply configuration
echo -e "${BLUE}🚀 Step 6: Deploy Infrastructure${NC}"

terraform apply tfplan
if [ $? -eq 0 ]; then
    print_status "Infrastructure deployed successfully!"
else
    print_error "Deployment failed"
    exit 1
fi

# Step 7: Test the deployment
echo -e "${BLUE}🧪 Step 7: Test Deployment${NC}"

SECRET_NAME=$(terraform output -raw secret_name)
SECRET_ARN=$(terraform output -raw secret_arn)

print_info "Testing secret retrieval..."

# Test 1: Get secret value
aws secretsmanager get-secret-value --secret-id "$SECRET_NAME" --query 'SecretString' --output text > /tmp/secret_test.json
if [ $? -eq 0 ]; then
    print_status "Successfully retrieved secret from AWS"
    
    # Parse and display (without showing actual keys)
    STRIPE_KEY_PREFIX=$(cat /tmp/secret_test.json | grep -o '"STRIPE_SECRET_KEY":"[^"]*"' | cut -d'"' -f4 | cut -c1-7)
    VERSION=$(cat /tmp/secret_test.json | grep -o '"VERSION":"[^"]*"' | cut -d'"' -f4)
    
    echo "   Secret contains Stripe key: ${STRIPE_KEY_PREFIX}..."
    echo "   Version: ${VERSION}"
    
    rm -f /tmp/secret_test.json
else
    print_error "Failed to retrieve secret"
fi

# Test 2: Test IAM policy
print_info "Testing IAM policy..."
POLICY_ARN=$(terraform output -raw iam_policy_arn)
aws iam get-policy --policy-arn "$POLICY_ARN" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status "IAM policy created successfully"
else
    print_error "IAM policy not found"
fi

# Step 8: Show next steps
echo ""
echo -e "${GREEN}🎉 Stripe Secrets Manager Test Completed!${NC}"
echo ""
echo -e "${BLUE}📋 What was created:${NC}"
echo "   Secret ARN: $SECRET_ARN"
echo "   Secret Name: $SECRET_NAME"
echo "   IAM Policy: $POLICY_ARN"
echo ""

echo -e "${BLUE}🔄 How to test key rotation:${NC}"
echo ""
echo "1. Update the secret with a new key:"
echo "   aws secretsmanager update-secret \\"
echo "     --secret-id \"$SECRET_NAME\" \\"
echo "     --secret-string '{\"STRIPE_SECRET_KEY\":\"sk_live_NEW_KEY\",\"STRIPE_ENDPOINT_SECRET\":\"whsec_...\",\"STRIPE_PUBLIC_KEY\":\"pk_live_...\",\"ROTATION_TIMESTAMP\":\"$(date -u +%Y-%m-%dT%H:%M:%SZ)\",\"VERSION\":\"2.0\"}'"
echo ""
echo "2. Verify the update:"
echo "   aws secretsmanager get-secret-value --secret-id \"$SECRET_NAME\""
echo ""

echo -e "${BLUE}🔧 How to use in Lambda functions:${NC}"
echo ""
echo "1. Add environment variable to your Lambda:"
echo "   STRIPE_SECRETS_NAME=$SECRET_NAME"
echo ""
echo "2. Attach IAM policy to Lambda role:"
echo "   aws iam attach-role-policy \\"
echo "     --role-name YOUR_LAMBDA_ROLE \\"
echo "     --policy-arn \"$POLICY_ARN\""
echo ""
echo "3. Update Lambda code to use the key manager (see implementation guide)"
echo ""

echo -e "${GREEN}✨ Benefits of this approach:${NC}"
echo "   • Secure storage of Stripe keys"
echo "   • One place to update keys (no more editing 20+ files)"
echo "   • Automatic key pickup by all Lambda functions"
echo "   • Zero downtime during key rotation"
echo "   • Comprehensive monitoring and alerting"
echo ""

# Clean up
rm -f tfplan

cd ../../../

print_info "For detailed implementation guide, see: STRIPE_KEY_ROTATION_IMPLEMENTATION_GUIDE.md"
