{"name": "theaplinestudio-project", "version": "1.0.0", "description": "The Alpine Studio", "main": "index.tsx", "scripts": {"start": "webpack-dev-server", "build": "webpack"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.20.5", "@babel/preset-env": "^7.20.2", "@babel/preset-react": "^7.18.6", "@types/leaflet": "^1.9.14", "babel-loader": "^9.1.0"}, "dependencies": {"@aws-sdk/client-cognito-identity-provider": "^3.569.0", "@babel/preset-typescript": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/icons-material": "^5.10.16", "@mui/material": "^5.10.17", "@stripe/react-stripe-js": "^2.3.0", "@stripe/stripe-js": "^2.1.5", "@types/body-parser": "^1.19.2", "@types/jest": "^29.2.4", "@types/node": "^18.11.12", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.9", "amazon-cognito-identity-js": "^6.3.12", "animejs": "^3.2.1", "axios": "^1.7.4", "css-loader": "^6.7.3", "dotenv": "^16.3.1", "file-loader": "^6.2.0", "framer-motion": "^11.11.11", "html-webpack-plugin": "^5.5.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "prettier": "2.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-intersection-observer": "^9.13.1", "react-leaflet": "^4.2.1", "react-qr-code": "^2.0.15", "react-router-dom": "^6.4.5", "react-scroll": "^1.8.9", "react-scroll-parallax": "^3.4.2", "react-toastify": "^9.1.3", "style-loader": "^3.3.1", "ts-loader": "^9.4.2", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.11.1"}}