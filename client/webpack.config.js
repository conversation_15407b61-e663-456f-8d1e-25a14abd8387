const HtmlWebpackPlugin = require("html-webpack-plugin");
const path = require("path");
const webpack = require("webpack");
require("dotenv").config();

module.exports = {
  entry: {
    main: "./src/index.tsx",
    admin: "./src/admin/index.tsx",
  },
  output: {
    path: path.join(__dirname, "build"),
    filename: "[name].bundle.js",
    publicPath: "/",
  },

  mode: "development",
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
  },
  devtool: "source-map",
  devServer: {
    static: path.join(__dirname, "src"),
    port: 5002,
    historyApiFallback: {
      rewrites: [
        { from: /^\/admin/, to: "/admin.html" }, // This line tells webpack-dev-server to use admin.html for paths under `/admin`
        { from: /./, to: "/index.html" },
      ],
    },
  },

  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: ["babel-loader"],
      },
      {
        test: /\.(ts|tsx)$/,
        exclude: /node_modules/,
        use: ["ts-loader"],
      },
      {
        test: /\.(css|scss)$/,
        use: ["style-loader", "css-loader"],
      },
      {
        test: /\.(jpg|jpeg|png|gif|mp3|webp|svg|avif)$/,
        use: ["file-loader"],
      },
    ],
  },
  plugins: [
    new HtmlWebpackPlugin({
      template: path.join(__dirname, "public", "/index.html"),
      chunks: ["main"],
      favicon: "./public/favicon.ico",
    }),
    new HtmlWebpackPlugin({
      template: path.join(__dirname, "public", "error.html"), 
      filename: "error.html",       // The final file name in build folder
      inject: false,                // Don’t insert any JS bundle links
    }),
    new HtmlWebpackPlugin({
      template: path.join(__dirname, "src", "admin", "public", "admin.html"),
      chunks: ["admin"],
      filename: "admin.html",
      favicon: "./public/favicon.ico",
    }),
    new webpack.DefinePlugin({
      REACT_APP_STRIPE_LIVE_SECRET_KEY: JSON.stringify(
        process.env.REACT_APP_STRIPE_LIVE_SECRET_KEY
      ),
      REACT_APP_TEST_STRIPE_SECRET_KEY: JSON.stringify(
        process.env.REACT_APP_TEST_STRIPE_SECRET_KEY
      ),
      REACT_APP_CART_API_ROUTE: JSON.stringify(
        process.env.REACT_APP_CART_API_ROUTE
      ),
      REACT_APP_PRODUCT_API_ROUTE: JSON.stringify(
        process.env.REACT_APP_PRODUCT_API_ROUTE
      ),
      REACT_APP_CONTACT_API_ROUTE: JSON.stringify(
        process.env.REACT_APP_CONTACT_API_ROUTE
      ),
      "process.env.REACT_APP_S3_BUCKET": JSON.stringify(process.env.REACT_APP_S3_BUCKET),
    
      // REACT_APP_PRESIGNUP_ROUTE: JSON.stringify(
      //   process.env.REACT_APP_PRESIGNUP_ROUTE
      // ),
      REACT_APP_PUBLIC_KEY: JSON.stringify(process.env.REACT_APP_PUBLIC_KEY),
      // REACT_APP_CLIENT_ID: JSON.stringify(process.env.REACT_APP_CLIENT_ID),
      // REACT_APP_USER_POOL_ID: JSON.stringify(
      //   process.env.REACT_APP_USER_POOL_ID
      // ),
      "process.env.REACT_APP_PUBLIC_KEY": JSON.stringify(process.env.REACT_APP_PUBLIC_KEY),

      // REACT_APP_IDENTITY_POOL_ID: JSON.stringify(process.env.REACT_APP_IDENTITY_POOL_ID),
      // REACT_APP_REGION: JSON.stringify(process.env.REACT_APP_REGION),
      // REACT_APP_ADMIN_CIENT_ID: JSON.stringify(process.env.REACT_APP_ADMIN_CIENT_ID),
      // REACT_APP_ADMIN_USER_POOL_ID: JSON.stringify(process.env.REACT_APP_ADMIN_USER_POOL_ID),
      // REACT_APP_ADMIN_COGNITO_DOMAIN: JSON.stringify(process.env.REACT_APP_ADMIN_COGNITO_DOMAIN),
      // REACT_APP_ADMIN_REDIRECT_SIGN_IN: JSON.stringify(process.env.REACT_APP_ADMIN_REDIRECT_SIGN_IN),
      // REACT_APP_ADMIN_REDIRECT_SIGN_OUT: JSON.stringify(process.env.REACT_APP_ADMIN_REDIRECT_SIGN_OUT),

      // "process.env.REACT_APP_MAINTENANCE_MODE": JSON.stringify(
      //   process.env.REACT_APP_MAINTENANCE_MODE
      // ),

      "process.env.TEST_REACT_APP_CONTACT_API_ROUTE": JSON.stringify(process.env.TEST_REACT_APP_CONTACT_API_ROUTE),
    }),
  ],
};
