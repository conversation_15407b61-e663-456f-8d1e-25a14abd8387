import { useLocation } from "react-router-dom";
import { useState, useCallback, useEffect } from "react";

const useSidebar = () => {
  const [sideBarOpen, setSideBarOpen] = useState(false);
  const location = useLocation();

  const handleMouseMove = useCallback((e: MouseEvent) => {
    const shouldOpen = e.clientX < 50;
    const shouldClose = e.clientX >= 50;

    if (shouldOpen && !sideBarOpen) {
      setSideBarOpen(true);
    } else if (shouldClose && sideBarOpen) {
      setSideBarOpen(false);
    }
  }, [sideBarOpen]);

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
    };
  }, [handleMouseMove]);

  const shouldShowSideBar = location.pathname.startsWith("/admin") && !location.pathname.includes("login");

  return { sideBarO<PERSON>, setSideBarOpen, shouldShowSideBar };
};

export default useSidebar;
