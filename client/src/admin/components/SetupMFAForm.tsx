import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Container, Paper, Typography, Button } from '@mui/material';
import { useTheme } from '@mui/material/styles'; // Import theme

const SetupMFA: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { username, session, qr_code_url, secret_code } = location.state;
  const theme = useTheme(); // Access theme colors

  const handleContinue = () => {
    navigate('/verify-mfa', { state: { username, session } });
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper
        elevation={3}
        sx={{
          mt: 8,
          padding: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          bgcolor: theme.palette.lightCyan.main, // Apply lightCyan background color
          boxShadow: `0px 4px 20px ${theme.palette.gray.main}`, // Apply gray shadow for depth
        }}
      >
        <Typography
          component="h1"
          variant="h5"
          sx={{
            color: theme.palette.rose.main, // Apply rose color to title
            mb: 2,
          }}
        >
          Set Up MFA
        </Typography>
        {qr_code_url && (
          <>
            <img src={qr_code_url} alt="QR Code for MFA" />
            <Typography
              component="p"
              sx={{
                color: theme.palette.cadetGray.main, // Apply cadetGray color to text
                mt: 2,
              }}
            >
              Secret Code: {secret_code}
            </Typography>
            <Button
              onClick={handleContinue}
              variant="contained"
              sx={{
                mt: 3,
                backgroundColor: theme.palette.lightBlue.main, // Use lightBlue for button
                '&:hover': {
                  backgroundColor: theme.palette.lightBlue.dark, // Darker shade on hover
                },
              }}
            >
              Continue
            </Button>
          </>
        )}
      </Paper>
    </Container>
  );
};

export default SetupMFA;
