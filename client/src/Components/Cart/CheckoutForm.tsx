import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
  CircularProgress,
  Grid,
} from "@mui/material";
import { CheckoutEvent, ShippingDetails } from "../../admin/auth/types";
import { useCart } from "../../Context/CartContext";
import OrderSummary from "./OrderSummary";

interface CheckoutFormProps {
  onCheckout: (event: CheckoutEvent) => void;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({ onCheckout }) => {
  const { cartItems } = useCart();

  // State for user details
  const [userDetails, setUserDetails] = useState<{
    name: string;
    email: string;
  }>({
    name: "",
    email: "",
  });

  // State for shipping details
  const [shippingDetails, setShippingDetails] = useState({
    line1: "",
    line2: "",
    city: "",
    state: "",
    postal_code: "",
    country: "US", // Default to US
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleUserDetailsSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (cartItems.length === 0) {
      setError("Your cart is empty.");
      return;
    }

    // Simple validation
    if (!userDetails.name || !userDetails.email) {
      setError("Please provide your name and email.");
      return;
    }

    if (
      !shippingDetails.line1 ||
      !shippingDetails.city ||
      !shippingDetails.state ||
      !shippingDetails.postal_code
    ) {
      setError("Please provide complete shipping address.");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    // Create a CheckoutEvent without address details (address will be collected on PaymentForm)
    const checkoutEvent: CheckoutEvent = {
      items: cartItems.map((item) => ({
        id: item.id,
        name: item.title,
        subcategory: item.selectedSubCategory,
        size: item.selectedSize?.size,
        currency: item.selectedPrice.currency,
        quantity: item.quantity,
        price: item.selectedPrice.amount,
        imageUrl: item.imageUrl,
      })),
      customer_email: userDetails.email,
      shipping_details: {
        address: {
          line1: shippingDetails.line1,
          line2: shippingDetails.line2,
          city: shippingDetails.city,
          state: shippingDetails.state,
          postal_code: shippingDetails.postal_code,
          country: shippingDetails.country,
        },
        name: userDetails.name,
      } as ShippingDetails,
      billing_details: {
        address: {},
        name: userDetails.name,
      } as ShippingDetails,
    };

    onCheckout(checkoutEvent);
    setIsSubmitting(false);
  };

  return (
    <form onSubmit={handleUserDetailsSubmit}>
      {error && (
        <Typography variant="body2" color="error" gutterBottom>
          {error}
        </Typography>
      )}
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <OrderSummary />
        </Grid>
        <Grid item xs={12} sm={6}>
          <Typography variant="h6" gutterBottom>
            Contact Information
          </Typography>
          <TextField
            label="Full Name"
            name="name"
            fullWidth
            required
            variant="outlined"
            value={userDetails.name}
            onChange={(e) =>
              setUserDetails({ ...userDetails, name: e.target.value })
            }
            sx={{ mb: 2 }}
          />
          <TextField
            label="Email"
            name="email"
            type="email"
            fullWidth
            required
            variant="outlined"
            value={userDetails.email}
            onChange={(e) =>
              setUserDetails({ ...userDetails, email: e.target.value })
            }
            sx={{ mb: 3 }}
          />

          <Typography variant="h6" gutterBottom>
            Shipping Address
          </Typography>
          <TextField
            label="Address Line 1"
            name="line1"
            fullWidth
            required
            variant="outlined"
            value={shippingDetails.line1}
            onChange={(e) =>
              setShippingDetails({ ...shippingDetails, line1: e.target.value })
            }
            sx={{ mb: 2 }}
          />
          <TextField
            label="Address Line 2 (Optional)"
            name="line2"
            fullWidth
            variant="outlined"
            value={shippingDetails.line2}
            onChange={(e) =>
              setShippingDetails({ ...shippingDetails, line2: e.target.value })
            }
            sx={{ mb: 2 }}
          />
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                label="City"
                name="city"
                fullWidth
                required
                variant="outlined"
                value={shippingDetails.city}
                onChange={(e) =>
                  setShippingDetails({
                    ...shippingDetails,
                    city: e.target.value,
                  })
                }
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                label="State"
                name="state"
                fullWidth
                required
                variant="outlined"
                value={shippingDetails.state}
                onChange={(e) =>
                  setShippingDetails({
                    ...shippingDetails,
                    state: e.target.value,
                  })
                }
                sx={{ mb: 2 }}
              />
            </Grid>
            <Grid item xs={12} sm={3}>
              <TextField
                label="ZIP Code"
                name="postal_code"
                fullWidth
                required
                variant="outlined"
                value={shippingDetails.postal_code}
                onChange={(e) =>
                  setShippingDetails({
                    ...shippingDetails,
                    postal_code: e.target.value,
                  })
                }
                sx={{ mb: 2 }}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      <Button
        type="submit"
        variant="contained"
        color="primary"
        fullWidth
        disabled={isSubmitting}
        sx={{
          py: 1.5,
          fontSize: "1rem",
          mt: 3,
          backgroundColor: "rose.main",
          "&:hover": {
            backgroundColor: "rose.light",
          },
        }}
      >
        {isSubmitting ? <CircularProgress size={24} /> : "Continue to Payment"}
      </Button>
    </form>
  );
};

export default CheckoutForm;
