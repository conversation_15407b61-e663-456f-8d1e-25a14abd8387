// src/components/Map/MapComponent.tsx

import React from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from "react-leaflet";
import { locations } from "../../data/locations";
import { Location } from "../../types/Location";
import L from "leaflet";
import { Box } from "@mui/material";
import "leaflet/dist/leaflet.css";

// Fix Leaflet's default icon issues
delete (L.Icon.Default.prototype as any)._getIconUrl;

L.Icon.Default.mergeOptions({
  iconRetinaUrl: require("leaflet/dist/images/marker-icon-2x.png").default,
  iconUrl: require("leaflet/dist/images/marker-icon.png").default,
  shadowUrl: require("leaflet/dist/images/marker-shadow.png").default,
});

// Optional: Adjust map bounds to fit all markers
const FitBounds = ({ locations }: { locations: Location[] }) => {
  const map = useMap();

  if (locations.length === 0) return null;

  const bounds = L.latLngBounds(locations.map((loc) => loc.position));
  map.fitBounds(bounds, { padding: [50, 50] });

  return null;
};

const MapComponent: React.FC = () => {
  // Optional: Define default center (if FitBounds is not used)
  const defaultCenter: [number, number] = [45.2, -110.5]; // Center between the three locations

  return (
    <Box
      sx={{
        width: "100%",
        height: { xs: "300px", sm: "400px", md: "500px" },
        mt: 6,
        borderRadius: 2,
        overflow: "hidden",
      }}
    >
      <MapContainer
        center={defaultCenter}
        zoom={7}
        scrollWheelZoom={false}
        style={{ height: "100%", width: "100%" }}
      >
        {/* Optional: Automatically fit bounds */}
        <FitBounds locations={locations} />

        {/* Choose a visually appealing tile layer */}
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* Render Markers */}
        {locations.map((location: Location, index: number) => (
          <Marker key={index} position={location.position}>
            <Popup>
              <strong>{location.name}</strong>
              <br />
              {location.description}
            </Popup>
          </Marker>
        ))}
      </MapContainer>
    </Box>
  );
};

export default MapComponent;
