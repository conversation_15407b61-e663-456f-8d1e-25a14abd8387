// src/Components/Wizard/GeneralContactForm.tsx

import React, { useState } from "react";
import {
  Button,
  <PERSON>Field,
  Typography,
  Box,
} from "@mui/material";
import { configContactForm } from "../../admin/auth/aws-config";

const url = configContactForm.RESEND_API_CONTACT_FORM;

if (!url) {
  throw new Error("RESEND_API_CONTACT_FORM must be defined");
}

const GeneralContactForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [name, setName] = useState("");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [emailStatus, setEmailStatus] = useState<"idle" | "success" | "error">(
    "idle"
  );

  const handleEmailChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(event.target.value);
  };

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const handleSubjectChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSubject(event.target.value);
  };

  const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setMessage(event.target.value);
  };

  const sendEmail = async () => {
    const emailContent = {
      formType: "generalInquiry",
      name,
      email,
      subject,
      message,
    };

    const requestBody = JSON.stringify(emailContent);

    // console.log("Sending general inquiry with content:", requestBody);

    try {
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: requestBody,
      });

      const responseData = await response.text();
      // console.log("Response status:", response.status);
      console.log("Response data:", responseData);

      if (response.status === 200) {
        setEmailStatus("success");
        setEmail("");
        setName("");
        setSubject("");
        setMessage("");

        setTimeout(() => setEmailStatus("idle"), 5000);
      } else {
        setEmailStatus("error");
      }
    } catch (error: any) {
      setEmailStatus("error");
      console.error("Failed to send email:", error.message);
    }
  };

  return (
    <Box>
      <TextField
        fullWidth
        margin="dense"
        label="Your Email"
        type="email"
        value={email}
        onChange={handleEmailChange}
        required
      />

      <TextField
        fullWidth
        margin="dense"
        label="Your Name"
        value={name}
        onChange={handleNameChange}
        required
      />

      <TextField
        fullWidth
        margin="dense"
        label="Subject"
        value={subject}
        onChange={handleSubjectChange}
        required
      />

      <TextField
        fullWidth
        margin="dense"
        label="Message"
        multiline
        rows={4}
        value={message}
        onChange={handleMessageChange}
        required
      />

      <Button
        variant="contained"
        onClick={sendEmail}
        sx={{ mt: 2, backgroundColor: "lightBlue.main", '&:hover': {
            backgroundColor: "rose.main", 
          }, }}
      >
        Send Message
      </Button>

      {emailStatus === "success" && (
        <Typography color="success.main" sx={{ mt: 2 }}>
          Message successfully sent!
        </Typography>
      )}
      {emailStatus === "error" && (
        <Typography color="error.main" sx={{ mt: 2 }}>
          Error sending message. Please try again.
        </Typography>
      )}
    </Box>
  );
};

export default GeneralContactForm;
