import React, { useState, useEffect, useMemo } from "react";
import {
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  Chip,
  Stack,
  CircularProgress,
  Box,
  useTheme,
  Pagination,
} from "@mui/material";
import { Product } from "../../admin/auth/types";

interface ProductGalleryProps {
  products: Product[];
  onSelectProduct: (product: Product) => void;
}

const ProductGallery: React.FC<ProductGalleryProps> = ({
  products,
  onSelectProduct,
}) => {
  // console.log("ProductGallery received products:", products);
  const theme = useTheme();

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6; // Adjust as needed
  const totalPages = Math.ceil(products.length / itemsPerPage);

  // Calculate indices for slicing
  const indexOfLastProduct = currentPage * itemsPerPage;
  const indexOfFirstProduct = indexOfLastProduct - itemsPerPage;
  const currentProducts = useMemo(() => {
    return products.slice(indexOfFirstProduct, indexOfLastProduct);
  }, [products, currentPage]);

  // Spinner logic: count how many images on the current page have loaded.
  const currentTotalImages = currentProducts.length;
  const [imagesLoaded, setImagesLoaded] = useState(0);

  // Reset the counter whenever the current page changes.
  useEffect(() => {
    setImagesLoaded(0);
  }, [currentProducts]);

  // Fallback timer: after 3 seconds, force the spinner off if not all images have loaded.
  useEffect(() => {
    const timer = setTimeout(() => {
      if (imagesLoaded < currentTotalImages) {
        setImagesLoaded(currentTotalImages);
      }
    }, 3000);
    return () => clearTimeout(timer);
  }, [currentTotalImages, imagesLoaded]);

  const handleImageLoad = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    setImagesLoaded((prev) => {
      const newCount = prev + 1;
      return newCount;
    });
  };

  const handleImageError = (
    e: React.SyntheticEvent<HTMLImageElement, Event>
  ) => {
    console.warn("⚠️ Image failed to load:", e.currentTarget.src);
    e.currentTarget.src = "/path-to-placeholder-image.jpg"; // fallback image
    setImagesLoaded((prev) => prev + 1);
  };

  const handlePageChange = (
    event: React.ChangeEvent<unknown>,
    page: number
  ) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  if (!products.length) {
    return <Typography variant="h6">No products found.</Typography>;
  }

  return (
    <Box
      sx={{
        backgroundColor: theme.palette.lightCyan.light,
        minHeight: "100vh",
        width: "100%",
        py: 4,
        px: { xs: 2, md: 4 },
      }}
    >
      <Box sx={{ maxWidth: "1200px", mx: "auto", textAlign: "center" }}>
        {imagesLoaded < currentTotalImages && (
          <Box sx={{ display: "flex", justifyContent: "center", mb: 4 }}>
            <CircularProgress />
          </Box>
        )}

        <Grid container spacing={2}>
          {currentProducts.map((product) => {
            const imageSrc = product.imageUrl;

            // Calculate minimum price from product sizes.
            const prices = product.sizes
              .map((size: any) => size.price.amount)
              .filter(
                (amount: number | undefined): amount is number =>
                  amount !== undefined
              );
            const minPrice = prices.length > 0 ? Math.min(...prices) : 0;
            const currency = product.sizes[0]?.price.currency || "USD";

            return (
              <Grid
                key={product.id}
                item
                xs={12}
                sm={6}
                md={4}
                onClick={() => onSelectProduct(product)}
              >
                <Card
                  sx={{
                    cursor: "pointer",
                    height: "100%",
                    transition: "box-shadow 0.3s ease",
                    "&:hover": { boxShadow: theme.shadows[6] },
                  }}
                >
                  <CardMedia
                    component="img"
                    height="400"
                    image={imageSrc}
                    alt={product.title}
                    sx={{
                      objectFit: "cover",
                      borderRadius: theme.shape.borderRadius,
                    }}
                    loading="lazy"
                    onLoad={handleImageLoad}
                    onError={handleImageError}
                  />
                  <CardContent>
                    <Typography
                      variant="h6"
                      sx={{
                        fontWeight: 500,
                        color: theme.palette.lightBlue.dark,
                      }}
                    >
                      {product.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Category: {product.category}
                    </Typography>
                    <Typography variant="body1" color="text.primary">
                      Price: {formatPrice(minPrice, currency)}
                    </Typography>
                    {product.sizes?.length > 0 && (
                      <Stack direction="row" spacing={1} mt={2} flexWrap="wrap">
                        {product.sizes.map((size: any, index: number) => (
                          <Chip
                            key={`${product.id}-size-${index}`}
                            label={size.size || "N/A"}
                            size="small"
                            variant="outlined"
                          />
                        ))}
                      </Stack>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {totalPages > 1 && (
          <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
            <Pagination
              count={totalPages}
              page={currentPage}
              onChange={handlePageChange}
              color="primary"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

function formatPrice(amount: number, currency: string): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
  }).format(amount);
}

export default React.memo(ProductGallery);
