// src/pages/Contact.tsx

import { 
  Box, 
  Typography, 
  Paper, 
  useTheme, 
  useMediaQuery, 
  Tabs, 
  Tab, 
} from "@mui/material";
import React, { lazy, useState } from "react";
const Wizard = lazy(() => import("../Components/Wizard/ContactWizard"));

const GeneralContactForm = lazy(() => import("../Components/Wizard/GeneralContactWizard"));

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`contact-tabpanel-${index}`}
      aria-labelledby={`contact-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const a11yProps = (index: number) => {
  return {
    id: `contact-tab-${index}`,
    'aria-controls': `contact-tabpanel-${index}`,
  };
};

const Contact = () => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const [tabValue, setTabValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Box
      id="contact"
      sx={{
        width: "100%",
        minHeight: "80vh",
        bgcolor: "lightBlue.main",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        padding: { xs: 2, sm: 4, md: 6 },
      }}
    >
      <Typography
        variant={isSmallScreen ? "h4" : "h3"}
        component="h1"

        sx={{
          pt: 8,
          letterSpacing: ".3rem",
          mb: 2,
          color: "rose.main",
          fontWeight: 700,
          textAlign: "center",
        }}
      >
        Contact Us
      </Typography>

      <Typography
        variant="h6"
        component="h2"
        sx={{
          mb: 4,
          color: "rose.main",
          textAlign: "center",
        }}
      >
        Choose the type of inquiry below.
      </Typography>

      <Paper
        elevation={3}
        sx={{
          width: "100%",
          maxWidth: 600,
          padding: { xs: 2, sm: 4 },
        }}
      >
        {/* Tabs for Switching Forms */}
        <Tabs
          value={tabValue}
          onChange={handleChange}
          variant="fullWidth"
          textColor="secondary"
          indicatorColor="secondary"
          aria-label="contact form tabs"
          sx={{
            mb: 3,
            "& .MuiTabs-indicator": {
              backgroundColor: theme.palette.lightBlue.main, // Custom indicator color
            },
            "& .MuiTab-root": {
              color: theme.palette.text.primary, // Default text color
              "&.Mui-selected": {
                color: theme.palette.rose.main, // Selected tab text color
              },
            },
          }}
        >
          <Tab label="Commission Request" {...a11yProps(0)} />
          <Tab label="General Questions" {...a11yProps(1)} />
        </Tabs>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          <Wizard color="primary" />
        </TabPanel>
        <TabPanel value={tabValue} index={1}>
          <GeneralContactForm />
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default Contact;
