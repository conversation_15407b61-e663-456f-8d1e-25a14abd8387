// src/pages/About.tsx

import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  useTheme,
  useMedia<PERSON><PERSON>y,
  Divider,
  CircularProgress,
} from "@mui/material";
import React, { Suspense, lazy } from "react";
import { motion } from "framer-motion";
import ariel from "../../assets/FestShop.avif";
import scratchboard from "../../assets/GreatHornedOwl.webp";
import { useInView } from "react-intersection-observer";
import { locations } from "../data/locations"; // Assuming you have a file with location data

const MapComponent = lazy(() => import("../Components/Map/LeafMap"));

// Define animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1, transition: { staggerChildren: 0.3 } },
};

const itemVariants = {
  hidden: { opacity: 0, y: 50 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.8, ease: "easeOut" } },
};

const About = ({ color }: { color: "primary" | "secondary" }) => {
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  //Setup the Intersection Observer
  const { ref, inView } = useInView({
    threshold: 0.2,
    triggerOnce: true,
  });

  return (
    <Box
      id="about"
      sx={{
        bgcolor: "lightBlue.main",
        py: { xs: 6, sm: 8, md: 10 },
      }}
    >
      <Container maxWidth="lg">
        {/* Section 1: Meet the Artist */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          <Box sx={{ textAlign: "center", mb: 8 }}>
            <motion.div variants={itemVariants}>
              <Typography
                variant={isSmallScreen ? "h4" : "h3"}
                component="h1"
                gutterBottom
                sx={{
                  pt: 8,
                  fontWeight: 700,
                  color: "rose.main",
                  letterSpacing: ".3rem",
                }}
              >
                Meet the Artist
              </Typography>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Grid
                container
                spacing={4}
                alignItems="center"
                justifyContent="center"
                sx={{ mt: 4 }}
              >
                {/* Artist Bio */}
                <Grid
                  item
                  xs={12}
                  md={8}
                  alignItems="center"
                  justifyContent="center"
                >
                  <Card
                    elevation={3}
                    sx={{ p: 3, bgcolor: "background.paper" }}
                  >
                    <CardContent
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Typography
                        variant="h5"
                        gutterBottom
                        sx={{
                          letterSpacing: ".3rem",
                          fontWeight: 600,
                          color: "text.primary",
                        }}
                      >
                        Ariel Rodriguez
                      </Typography>
                      <Divider sx={{ mb: 2, bgcolor: "lightCyan.main" }} />

                      {/* Artist Image */}
                      <Grid item xs={12} md={6}>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            p: 2,
                          }}
                        >
                          <img
                            src={ariel}
                            alt="Ariel Rodriguez"
                            style={{
                              width: "100%",
                              height: "auto",
                              borderRadius: "8px",
                              boxShadow: theme.shadows[3],
                            }}
                          />
                        </Box>
                      </Grid>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          textAlign: isSmallScreen ? "center" : "left", // Centers text on small screens
                        }}
                        paragraph
                      >
                        Based in Gardiner, Montana, just outside the iconic Yellowstone National Park, Ariel Rodriguez is a talented wildlife scratchboard artist whose intricate works capture the beauty and power of nature. Specializing in wildlife and bird subjects, Ariel uses an X-Acto knife and watercolor to create detailed, captivating pieces that evoke the spirit of the wild.
                      </Typography>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          textAlign: isSmallScreen ? "center" : "left", // Centers text on small screens
                        }}
                        paragraph
                      >
                        Each of Ariel's creations is meticulously etched with precision, using the scratchboard technique to bring texture and life to every feather, fur, and feature. The addition of watercolor enhances the depth and vibrancy of the work, creating a striking contrast between the delicate scratches and the fluidity of the paint.
                      </Typography>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          textAlign: isSmallScreen ? "center" : "left", // Centers text on small screens
                        }}
                        paragraph
                      >
                        Ariel's artwork can be found throughout the country, in galleries, exhibits, and shops, with a special presence in Yellowstone National Park, where the artist's work resonates with both wildlife enthusiasts and visitors to the park. With a deep appreciation for the natural world, Ariel strives to bring attention to the majestic creatures that roam the American landscape, capturing their essence with every stroke of the blade and splash of paint.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          </Box>
        </motion.div>

        {/* Section 2: What is a Scratchboard? */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          style={{ marginTop: 12 }}
        >
          <Box sx={{ textAlign: "center", mb: 4 }}>
            <motion.div variants={itemVariants}>
              <Grid
                container
                spacing={4}
                alignItems="center"
                justifyContent="center"
                sx={{ mt: 4 }}
              >
                {/* Scratchboard Description */}
                <Grid
                  item
                  xs={12}
                  md={8}
                  alignItems="center"
                  justifyContent="center"
                >
                  <Card
                    elevation={3}
                    sx={{ p: 3, bgcolor: "background.paper" }}
                  >
                    <CardContent
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Typography
                        variant="h6"
                        gutterBottom
                        sx={{
                          letterSpacing: ".1rem",
                          fontWeight: 600,
                          color: "text.primary",
                        }}
                      >
                        Understanding Scratchboards
                      </Typography>
                      <Divider sx={{ mb: 2, bgcolor: "lightCyan.main" }} />

                      {/* Scratchboard Image */}
                      <Grid item xs={12} md={6}>
                        <Box
                          sx={{
                            display: "flex",
                            justifyContent: "center",
                            p: 2,
                          }}
                        >
                          <img
                            src={scratchboard}
                            alt="Scratchboard Example"
                            style={{
                              width: "100%",
                              height: "auto",
                              borderRadius: "8px",
                              boxShadow: theme.shadows[3],
                            }}
                          />
                        </Box>
                      </Grid>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        paragraph
                      >
                        Scratchboards are white clay boards coated with a layer
                        of India Black ink. Artists like Ariel use a specialized
                        knife to scratch away the ink, creating different shades
                        and textures. By varying the pressure and angle, they
                        can craft detailed and dynamic images, bringing subjects
                        to life with depth and realism.
                      </Typography>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        paragraph
                      >
                        While traditionally black and white, artists can
                        incorporate colored pigments using brushes to add depth
                        and vibrancy to their work. This meticulous and
                        unforgiving process demands precision and a steady hand,
                        leaving no room for error.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </motion.div>
          </Box>
        </motion.div>

        {/* Section 3: Where to Find Ariel’s Art */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
          style={{ marginTop: 12 }}
        >
          <Box
            sx={{
              textAlign: "center",
              mb: { xs: 4, sm: 6 }, // Increase bottom margin on mobile if needed
              px: { xs: 2, sm: 0 }, // Add horizontal padding on small screens
            }}
          >
            {/* Section 3: Where to Find Ariel’s Art */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true, amount: 0.2 }}
              style={{ marginTop: 12 }}
            >
              <Box
                sx={{
                  textAlign: "center",
                  mb: { xs: 4, sm: 6 },
                  px: { xs: 2, sm: 0 },
                }}
              >
                <motion.div variants={itemVariants}>
                  <Typography
                    variant={isSmallScreen ? "h5" : "h4"}
                    component="h2"
                    gutterBottom
                    sx={{
                      letterSpacing: ".2rem",
                      fontWeight: 700,
                      color: "rose.main",
                    }}
                  >
                    Where to Find Ariel’s Art
                  </Typography>
                </motion.div>
              </Box>

              <Grid
                container
                spacing={4}
                justifyContent="center"
                sx={{ px: { xs: 2, sm: 0 } }}
              >
                {locations.map((loc) => (
                  <Grid
                    item
                    key={loc.name}
                    xs={12}
                    sm={6}
                    md={3}
                    sx={{ display: "flex", justifyContent: "center" }}
                  >
                    <motion.div variants={itemVariants} style={{ flex: 1 }}>
                      <Box
                        sx={{
                          textAlign: "center",
                          p: 3,
                          bgcolor: "background.paper",
                          boxShadow: 3,
                          borderRadius: 2,
                          minHeight: "220px", // set a consistent minimum height
                          display: "flex",
                          flexDirection: "column",
                          justifyContent: "center",
                          transition: "transform 0.3s, box-shadow 0.3s",
                          "&:hover": {
                            transform: "translateY(-10px)",
                            boxShadow: 6,
                          },
                          overflow: "hidden",
                        }}
                      >
                        <Typography
                          variant="h6"
                          gutterBottom
                          sx={{
                            letterSpacing: ".1rem",
                            color: "lightBlue.main",
                            fontWeight: 600,
                          }}
                        >
                          {loc.name}
                        </Typography>
                        <Typography
                          variant="body2"
                          gutterBottom
                          sx={{
                            letterSpacing: ".02rem",
                            color: "rose.light",
                            fontWeight: 300,
                            fontStyle: "italic",
                          }}
                        >
                          Address: {loc.address}
                        </Typography>

                        <Typography variant="body2"                           sx={{
                            letterSpacing: ".02rem",
                            color: "rose.main",
                            fontWeight: 600,
                          }}>
                          {loc.description}
                        </Typography>
                      </Box>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
              {/* Map section can follow here */}
            </motion.div>
          </Box>

          {/* Insert the Map Component Below the Grid */}
          <Box sx={{ mt: 6, boxShadow: 3, borderRadius: 2 }} ref={ref}>
            <motion.div variants={itemVariants}>
              {inView && (
                <Suspense
                  fallback={
                    <Box sx={{ height: 400, bgcolor: "background.paper" }}>
                      <CircularProgress />
                    </Box>
                  }
                >
                  <MapComponent />
                </Suspense>
              )}
            </motion.div>
          </Box>
        </motion.div>
      </Container>
    </Box>
  );
};

export default About;
