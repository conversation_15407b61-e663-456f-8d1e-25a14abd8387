import { Box, Container, IconButton, Typography } from "@mui/material";
import Ariel<PERSON>ig from "../Components/Signature/ArielSig";
import React from "react";
import InstagramIcon from "@mui/icons-material/Instagram";
import CategoriesGrid from "../Components/CategoriesGrid/CategoriesGrid";
import { useNavigate } from "react-router-dom";
import { QUERY_PARAMS } from "../contants/queryParams";
import { categories, Category } from "../admin/auth/types";
// import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';



const Landing = ({ color }: { color: "primary" | "secondary" }) => {
  const navigate = useNavigate();
  const handleCategoryClick = (category: Category) => {
    // Ensure 'Category' type is used
    console.log("Navigating to category:", category); // Debug log
    navigate(
      `/shop?${QUERY_PARAMS.CATEGORY}=${encodeURIComponent(category)}&${
        QUERY_PARAMS.SORT
      }=asc&${QUERY_PARAMS.PAGE}=1&${QUERY_PARAMS.LIMIT}=12`
    );
  };

  return (
    <div id="/">
      <Box
        sx={{
          width: "100%",
          minHeight: "80vh",
          bgcolor: "gray.light",
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          padding: { xs: 2, sm: 4, md: 6 },
        }}
      >
        <ArielSig />
        <Typography
          variant="h1"
          sx={{
            textAlign: "center",
            pt: 5,
            letterSpacing: ".3rem",
            fontSize: {
              xs: "2.8rem",
              sm: "3rem",
              md: "3.4rem",
              lg: "3.8rem",
              xl: "4rem",
            },
          }}
        >
          The Alpine Studio
        </Typography>

        <Typography
          variant="h4"
          sx={{
            textAlign: "center",
            pt: 2,
            pb: 2,
            color: "rose.main",
            letterSpacing: ".3rem",
            fontWeight: 300,
            fontSize: {
              xs: "1.2rem",
              sm: "1.4rem",
              md: "1.6rem",
              lg: "1.8rem",
              xl: "1.10rem",
            },
          }}
        >
          Wilderness Wonders, Detailed by Hand
        </Typography>
        <IconButton
          component="a"
          href="https://www.instagram.com/alpine.studio.art/"
          target="_blank"
          rel="noopener noreferrer"
          color="inherit"
          sx={{ display: "flex", justifyContent: "center" }}
        >
          <InstagramIcon />
        </IconButton>
      </Box>
      <Container maxWidth="lg" sx={{ mt: 4 }}>
        {/* If you’d like to introduce a title above the categories grid */}
        <Typography
          variant="h3"
          align="center"
          sx={{
            color: "rose.main",
            fontWeight: 300,
            letterSpacing: ".2rem",
            mb: 4,
          }}
        >
          Explore The Collections
        </Typography>
        <CategoriesGrid onCategoryClick={handleCategoryClick} categories={categories} />
      </Container>

    </div>
  );
};

export default Landing;
