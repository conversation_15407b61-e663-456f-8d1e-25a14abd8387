// src/utils/dynamoDBUtils.ts

import { Product } from "admin/auth/types";

type DynamoDBAttributeValue =
  | { S: string }
  | { N: string }
  | { BOOL: boolean }
  | { L: DynamoDBAttributeValue[] }
  | { M: { [key: string]: DynamoDBAttributeValue } }
  | { NULL: boolean };


/**
 * Recursively unmarshalls a DynamoDB AttributeValue object into plain JSON.
 * @param data - The DynamoDB AttributeValue object.
 * @returns The unmarshalled plain JSON object.
 */
export const unmarshallDynamoDBItem = (
  data: { [key: string]: DynamoDBAttributeValue }
): Product => {
  const unmarshallValue = (value: DynamoDBAttributeValue): any => {
    if ('S' in value) {
      return value.S;
    }
    if ('N' in value) {
      return Number(value.N);
    }
    if ('BOOL' in value) {
      return value.BOOL;
    }
    if ('NULL' in value) {
      return null;
    }
    if ('L' in value) {
      return value.L.map((item) => unmarshallValue(item));
    }
    if ('M' in value) {
      const obj: { [key: string]: any } = {};
      for (const key in value.M) {
        obj[key] = unmarshallValue(value.M[key]);
      }
      return obj;
    }
    return undefined;
  };

  const unmarshalled: any = {};
  for (const key in data) {
    unmarshalled[key.toLowerCase()] = unmarshallValue(data[key]);
  }

  // Transform the unmarshalled data into the Product interface
  const product: Product = {
    id: unmarshalled.id || "Unknown ID",
    title: unmarshalled.title || "Untitled Product",
    description: unmarshalled.description || "No description available.",
    category: unmarshalled.category || "Other",
    subcategories: unmarshalled.subcategories || [],
    sizes: Array.isArray(unmarshalled.sizes)
      ? unmarshalled.sizes.map((sizeObj: any) => ({
          size: sizeObj.size || "N/A",
          price: {
            amount: sizeObj.price?.amount || 0,
            currency: sizeObj.price?.currency || "USD",
          },
        }))
      : [],
    imageUrl: unmarshalled.imageurl || "/default-image.jpg",
    sold: unmarshalled.sold || false,
  };

  console.log("Unmarshalled Product:", product); // Debugging log
  return product;
};
