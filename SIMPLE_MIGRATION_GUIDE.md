# Simple Stripe Key Rotation - Minimal Changes Approach

## The Problem You're Solving

Every 7 days you have to:
1. Generate new Stripe key (with 2FA) ✅ Still manual (can't be automated)
2. Update `terraform.tfvars` files ❌ Manual, error-prone
3. Redeploy Lambda functions ❌ Causes downtime
4. Update multiple configuration files ❌ Easy to miss one

## The Simple Solution

Instead of updating files and redeploying, just:
1. Generate new Stripe key (with 2FA) ✅ Still manual (can't be automated)
2. Update ONE secret in AWS ✅ One command
3. All Lambda functions automatically get new key ✅ No redeployment needed

## Minimal Code Changes

### Step 1: Add Helper Function to Your Existing Lambda

Copy the `simple_secrets_helper.go` code into your existing `checkoutProducts.go` file.

### Step 2: Change 3 Lines in Your Existing Function

**Before (current code):**
```go
func CheckoutProductHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // ... existing code ...
    
    // OLD: Get key from environment variable
    stripeKey := os.Getenv("STRIPE_SECRET_KEY")
    stripe.Key = stripeKey
    
    // ... rest of your existing code stays the same ...
}
```

**After (3 line change):**
```go
func CheckoutProductHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    // ... existing code stays exactly the same ...
    
    // NEW: Get key from Secrets Manager OR environment variable
    stripeKey := getStripeKey()  // <-- Only change line 1
    if stripeKey == "" {         // <-- Only change line 2
        return events.APIGatewayProxyResponse{StatusCode: 500, Body: `{"error":"Stripe key not configured"}`}, nil  // <-- Only change line 3
    }
    stripe.Key = stripeKey
    
    // ... rest of your existing code stays exactly the same ...
}
```

That's it! Your existing function now supports both approaches.

## How This Works

### Current State (Environment Variable):
```
Lambda Function → Environment Variable → Stripe Key
```

### New State (Secrets Manager with Fallback):
```
Lambda Function → AWS Secrets Manager → Stripe Key
                ↘ (if fails) Environment Variable → Stripe Key
```

## Testing Strategy

### Phase 1: Deploy Infrastructure Only
```bash
cd terraform/testing/test-secrets-manager
cp terraform.tfvars.example terraform.tfvars
# Edit with your current Stripe keys
terraform apply
```

### Phase 2: Test Secret Retrieval
```bash
# Verify the secret was created
aws secretsmanager get-secret-value --secret-id "test/stripe/api-keys"
```

### Phase 3: Update Lambda Function
1. Add the helper function to your existing `checkoutProducts.go`
2. Change the 3 lines as shown above
3. Add `STRIPE_SECRETS_NAME` environment variable to your Lambda
4. Deploy the updated function

### Phase 4: Test Both Approaches
1. **Test with Secrets Manager:** Set `STRIPE_SECRETS_NAME=test/stripe/api-keys`
2. **Test with Environment Variable:** Remove `STRIPE_SECRETS_NAME`, keep `STRIPE_SECRET_KEY`
3. **Test Fallback:** Set both, verify it uses Secrets Manager first

## Key Rotation Workflow

### Current Workflow (Every 7 Days):
1. Generate new Stripe key in dashboard (2FA)
2. Edit `terraform/testing/test-env/terraform.tfvars`
3. Edit `terraform/staging/terraform.tfvars`
4. Edit `terraform/production/terraform.tfvars`
5. Run `terraform apply` for each environment
6. Wait for Lambda functions to redeploy
7. Test all payment flows
8. Hope you didn't miss any files

### New Workflow (Every 7 Days):
1. Generate new Stripe key in dashboard (2FA)
2. Run one command:
   ```bash
   aws secretsmanager update-secret --secret-id "test/stripe/api-keys" --secret-string '{"STRIPE_SECRET_KEY":"sk_live_NEW_KEY","STRIPE_ENDPOINT_SECRET":"whsec_...","VERSION":"2.0"}'
   ```
3. Done! All Lambda functions get the new key automatically

## Benefits

✅ **No Downtime:** Lambda functions don't need redeployment  
✅ **No File Editing:** One secret update instead of multiple file edits  
✅ **Automatic Fallback:** If Secrets Manager fails, uses environment variable  
✅ **Gradual Migration:** Can test with one function first  
✅ **Zero Risk:** Existing approach still works as backup  

## Architecture

You asked about having a separate Lambda function handle key rotation. Here's why the simple approach is better:

### ❌ Complex Approach (Separate Rotation Lambda):
```
Stripe Dashboard → You → Rotation Lambda → Secrets Manager → Checkout Lambda
```
- More moving parts
- More things that can fail
- Need to manage another Lambda function
- Need to handle communication between Lambdas

### ✅ Simple Approach (Direct Access):
```
Stripe Dashboard → You → Secrets Manager → Checkout Lambda
```
- Fewer moving parts
- Each Lambda is independent
- Direct access to secrets
- Easier to debug and maintain

## Next Steps

1. **Start with infrastructure:** Deploy the Secrets Manager setup
2. **Test one function:** Update just `checkoutProducts` first
3. **Verify it works:** Test both secret and environment variable approaches
4. **Scale gradually:** Apply to other Lambda functions once proven
5. **Clean up:** Remove environment variables once all functions are migrated

The beauty of this approach is that it's **backward compatible** - your existing setup keeps working while you gradually migrate to the new approach.
