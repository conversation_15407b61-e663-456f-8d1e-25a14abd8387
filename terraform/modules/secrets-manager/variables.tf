variable "environment" {
  type        = string
  description = "Environment name (dev, test, staging, prod)"

  validation {
    condition     = contains(["dev", "test", "staging", "prod"], var.environment)
    error_message = "Environment must be one of: dev, test, staging, prod."
  }
}

variable "stripe_secret_key" {
  type        = string
  description = "Initial Stripe secret key"
  sensitive   = true
}

variable "stripe_endpoint_secret" {
  type        = string
  description = "Initial Stripe webhook endpoint secret"
  sensitive   = true
}

variable "stripe_public_key" {
  type        = string
  description = "Stripe publishable key"
  default     = ""
}

variable "rotation_days" {
  type        = number
  description = "Number of days between automatic rotations"
  default     = 7

  validation {
    condition     = var.rotation_days >= 1 && var.rotation_days <= 365
    error_message = "Rotation days must be between 1 and 365."
  }
}

variable "replica_regions" {
  type        = list(string)
  description = "List of regions for cross-region secret replication"
  default     = []
}

variable "common_tags" {
  type        = map(string)
  description = "Common tags to apply to all resources"
  default     = {}
}

variable "log_retention_days" {
  type        = number
  description = "CloudWatch log retention period in days"
  default     = 30

  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.log_retention_days)
    error_message = "Log retention days must be a valid CloudWatch retention period."
  }
}

variable "access_threshold" {
  type        = number
  description = "Threshold for unusual access pattern alerts"
  default     = 100
}

variable "alarm_sns_topics" {
  type        = list(string)
  description = "List of SNS topic ARNs for alarm notifications"
  default     = []
}

variable "enable_cross_region_replication" {
  type        = bool
  description = "Enable cross-region replication for disaster recovery"
  default     = false
}

variable "kms_key_id" {
  type        = string
  description = "KMS key ID for encrypting secrets (optional)"
  default     = null
}

# Removed rotation_lambda_arn variable since we're using manual rotation
