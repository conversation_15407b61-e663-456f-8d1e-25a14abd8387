output "secret_arn" {
  description = "ARN of the Stripe secrets"
  value       = aws_secretsmanager_secret.stripe_keys.arn
}

output "secret_name" {
  description = "Name of the Stripe secrets"
  value       = aws_secretsmanager_secret.stripe_keys.name
}

output "secret_id" {
  description = "ID of the Stripe secrets"
  value       = aws_secretsmanager_secret.stripe_keys.id
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for accessing Stripe secrets"
  value       = aws_iam_policy.stripe_secrets_access.arn
}

output "replica_arns" {
  description = "ARNs of replicated secrets in other regions (feature removed)"
  value       = []
}

output "cloudwatch_log_group_name" {
  description = "Name of the CloudWatch log group for audit logs"
  value       = aws_cloudwatch_log_group.stripe_secrets_audit.name
}

output "cloudwatch_alarm_name" {
  description = "Name of the CloudWatch alarm for unusual access patterns"
  value       = aws_cloudwatch_metric_alarm.unusual_secret_access.alarm_name
}
