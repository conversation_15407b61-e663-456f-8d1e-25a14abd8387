# AWS Secrets Manager for Stripe API Keys
# Production-grade implementation with automatic rotation support

locals {
  environment = var.environment
  secret_name = "${local.environment}/stripe/api-keys"
}

# Main secret for Stripe API keys
resource "aws_secretsmanager_secret" "stripe_keys" {
  name        = local.secret_name
  description = "Stripe API keys for ${local.environment} environment"

  # Note: Automatic rotation is not possible with Stripe due to 2FA requirements
  # We'll use CloudWatch Events and manual notification instead

  tags = merge(var.common_tags, {
    Environment  = local.environment
    Service      = "stripe"
    Rotation     = "manual-notification"
    RotationDays = tostring(var.rotation_days)
  })
}

# Initial secret version with current keys
resource "aws_secretsmanager_secret_version" "stripe_keys_initial" {
  secret_id = aws_secretsmanager_secret.stripe_keys.id

  secret_string = jsonencode({
    STRIPE_SECRET_KEY      = var.stripe_secret_key
    STRIPE_ENDPOINT_SECRET = var.stripe_endpoint_secret
    STRIPE_PUBLIC_KEY      = var.stripe_public_key
    ROTATION_TIMESTAMP     = timestamp()
    VERSION                = "1.0"
  })

  lifecycle {
    ignore_changes = [secret_string]
  }
}

# Note: Cross-region replication removed due to AWS provider limitations
# For disaster recovery, consider using AWS Backup or manual replication scripts

# IAM policy for Lambda functions to access secrets
resource "aws_iam_policy" "stripe_secrets_access" {
  name        = "${local.environment}-stripe-secrets-access"
  description = "Policy for accessing Stripe secrets in ${local.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "secretsmanager:GetSecretValue",
          "secretsmanager:DescribeSecret"
        ]
        Resource = [
          aws_secretsmanager_secret.stripe_keys.arn,
          "${aws_secretsmanager_secret.stripe_keys.arn}:*"
        ]
        Condition = {
          StringEquals = {
            "aws:ResourceTag/Environment" = local.environment
          }
        }
      }
    ]
  })

  tags = var.common_tags
}

# CloudWatch Log Group for secret access monitoring
resource "aws_cloudwatch_log_group" "stripe_secrets_audit" {
  name              = "/aws/secretsmanager/${local.environment}/stripe"
  retention_in_days = var.log_retention_days

  tags = merge(var.common_tags, {
    Environment = local.environment
    Service     = "stripe-secrets"
  })
}

# CloudWatch Metric Filter for monitoring secret access
resource "aws_cloudwatch_log_metric_filter" "secret_access_count" {
  name           = "${local.environment}-stripe-secret-access"
  log_group_name = aws_cloudwatch_log_group.stripe_secrets_audit.name
  pattern        = "[timestamp, request_id, event_name=\"GetSecretValue\"]"

  metric_transformation {
    name      = "StripeSecretAccess"
    namespace = "CustomMetrics/SecretsManager"
    value     = "1"

    default_value = 0

    dimensions = {
      Environment = local.environment
      SecretName  = local.secret_name
    }
  }
}

# CloudWatch Alarm for unusual access patterns
resource "aws_cloudwatch_metric_alarm" "unusual_secret_access" {
  alarm_name          = "${local.environment}-stripe-unusual-access"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "StripeSecretAccess"
  namespace           = "CustomMetrics/SecretsManager"
  period              = "300"
  statistic           = "Sum"
  threshold           = var.access_threshold
  alarm_description   = "Unusual access pattern detected for Stripe secrets"
  alarm_actions       = var.alarm_sns_topics

  dimensions = {
    Environment = local.environment
    SecretName  = local.secret_name
  }

  tags = var.common_tags
}

# Note: CloudWatch Event Rules for automatic rotation removed
# Since we're using manual rotation process, no automated scheduling needed
