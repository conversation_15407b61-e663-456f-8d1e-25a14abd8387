//CHECKOUT PRODUCTS FUNCTION

data "archive_file" "lambda_code_checkout" {
  type        = "zip"
  source_dir  = "../../lambda/"
  output_path = "./checkout-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_iam_role" "lambda_execution_role_checkout" {
  name = "lambda_execution_role_checkout_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_checkout" {
  name = "lambda_execution_policy_checkout_products_function"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket}/*",
          "arn:aws:s3:::${var.s3_bucket}/*"
          ]

      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:*:*:*"
      }
    ]
  })
}

//CHECKOUT PRODUCTS FUNCTION
resource "aws_s3_object" "lambda_code_checkout" {
  bucket       = var.s3_bucket
  key          = "checkout-products.zip"
  source       = data.archive_file.lambda_code_checkout.output_path
  etag         = filemd5(data.archive_file.lambda_code_checkout.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "checkout_products_function" {
  function_name = var.checkout_products_function
  s3_bucket     = aws_s3_object.lambda_code_checkout.bucket
  s3_key        = aws_s3_object.lambda_code_checkout.key
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "checkout-products"
  runtime          = "provided.al2"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_checkout.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      STRIPE_SECRET_KEY = var.stripe_secret_key
    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_checkout" {
  statement_id  = "AllowExecutionFromAPIGatewayV1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.checkout_products_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "${var.api_gateway_arn}/*/*"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_GET_checkout" {
  statement_id  = "AllowExecutionFromAPIGatewayGET"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.checkout_products_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/GET/checkout-products"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_POST_checkout" {
  statement_id  = "AllowExecutionFromAPIGatewayPOST"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.checkout_products_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/POST/checkout-products"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "null_resource" "remove_lambda_permission_checkout" {
  triggers = {
    statement_id = aws_lambda_permission.lambda_api_gw_permissions.statement_id
  }
  provisioner "local-exec" {
  environment = {
    AWS_DEFAULT_REGION = "us-west-2"
  }
  command = "aws lambda remove-permission --function-name arn:aws:lambda:us-west-2:************:function:checkout-products --statement-id AllowExecutionFromAPIGatewayV1"
}

depends_on = [
  aws_lambda_permission.lambda_api_gw_permissions,
]

# Only run the provisioner when the statement ID changes
# and ignore other changes in the null_resource
lifecycle {
  create_before_destroy = true
  ignore_changes        = all
}
}