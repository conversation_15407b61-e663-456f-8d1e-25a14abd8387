variable "s3_bucket" {
    type = string
    description = "The name of the S3 bucket to store the lambda"
    default = "dev-thealpinestudio-lambda-v1"
}


variable "description" {
  type = string
  default = "List Products function"
}

variable "lambda_iam_role_name" {
  type = string
  default = "lambda_arn_role"
}

# variable "cognito_user_pool_arn" {
#   type        = string
#   description = "The ARN of the user pool"
# }

variable "api_gateway_arn" {
  type = string
  description = "The ARN of the api-gateway"
}

variable "api_gateway_id" {
  type = string
  description = "The ID of the api-gateway"
}

variable "api_gateway_region" {
  type = string
  description = "region"
  default = "us-west-2"
}

variable "api_gateway_account_id" {
  type = string
  description = "account id"
}

variable "stripe_secret_key" {
  type = string
  description = "Stripe Secret Key"
}

variable "stripe_endpoint_secret" {
  type = string
  description = "Stripe Endpoint Secret"
}
variable "lambda_function_arn" {
  type = string
  description = "Lambda function ARN"
}

variable "list_products_function" {
  type = string
  description = "the name of the lambda list function"
  default = "list-products"
}

variable "checkout_products_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "checkout-products"
}

variable "webhooks_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "webhooks"
}

variable "contact_email_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "contact-email"
}

