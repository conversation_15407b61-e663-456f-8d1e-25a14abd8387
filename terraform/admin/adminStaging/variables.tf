//adminStaging/variables.tf
variable "aws_region" {
  description = "The AWS region"
  type        = string

}

variable "aws_account_id" {
  description = "The AWS account ID"
  type        = string

}
variable "sns_topic_name" {
  description = "The name of the SNS topic"
  type        = string

}

variable "api_gateway_account_id" {
  description = "The ID of the AWS account that owns the API Gateway"
  type        = string

}

variable "presign_lambda_function_name" {
  type        = string
  description = "Name of the presigned URL Lambda function"
}

variable "image_bucket_name" {
  description = "The name of the S3 bucket to store images"
  type        = string

}

variable "resizer_lambda_function_name" {
  description = "The name of the resizer Lambda function"
  type        = string
}

variable "s3_bucket" {
  type        = string
  description = "The name of the S3 bucket to store the lambda functions"
}

variable "admin_bucket_name" {
  type        = string
  description = "Name of the Admin S3 bucket. Must be unique."
}

variable "admin_domain_name" {
  description = "The domain name for the API Gateway"
  type        = string

}

variable "client_id" {
  description = "The client ID for the Cognito User Pool"
  type        = string

}

variable "client_secret" {
  description = "The client secret for the Cognito User Pool"
  type        = string
}

variable "admin_s3_sub_domain" {
  description = "value of the subdomain for the S3 bucket"
  type        = string
}
variable "admin_sub_domain" {
  description = "The subdomain for the API Gateway"
  type        = string
}

variable "zone_id" {
  description = "The Zone ID of the Route53 zone"
  type        = string

}

variable "admin_api_gateway" {
  description = "The name of the API Gateway"
  type        = string
  default     = "admin-api"

}

variable "root_domain" {
  description = "The root domain for the API Gateway"
  type        = string

}

variable "dynamo_table_products_name" {
  description = "The name of the DynamoDB table"
  type        = string

}

variable "get_lambda_function_name" {
  type        = string
  description = "The name of the GET Lambda function"
}

variable "get_product_by_id_lambda_function_name" {
  description = "The name of the Lambda function that retrieves a product by ID"
  type        = string
  
}

variable "put_lambda_function_name" {
  type        = string
  description = "The name of the PUT Lambda function"

}

variable "post_lambda_function_name" {
  type        = string
  description = "The name of the POST Lambda function"

}

variable "delete_lambda_function_name" {
  type        = string
  description = "The name of the DELETE Lambda function"

}
variable "ses_sender" {
  description = "The email address of the sender"
  type        = string
}

variable "auth_cognito_refresh_token_function_name" {
  type        = string
  description = "lambda function name for cognito refresh token"
}


//login name
variable "auth_cognito_login_function_name" {
  description = "lambda function name for cognito login"
  type        = string
}
//logout name
variable "auth_cognito_logout_function_name" {
  description = "lambda function name for cognito logout"
  type        = string
}

variable "auth_cognito_verify_mfa_function_name" {
  description = "lambda function name for cognito verify mfa"
  type        = string
}


//forgot password name
variable "auth_cognito_forgot_password_function_name" {
  description = "lambda function name for cognito forgot password"
  type        = string
}


//resend code name
variable "auth_cognito_resend_code_function_name" {
  description = "lambda function name for cognito resend code"
  type        = string
}

//pre sign up name
variable "auth_cognito_pre_signup_function_name" {
  description = "lambda function name for cognito pre sign up"
  type        = string
}

//sign up name
variable "auth_cognito_signup_function_name" {
  description = "lambda function name for cognito sign up"
  type        = string
}

variable "auth_cognito_confirm_signup_function_name" {
  description = "lambda function name for cognito confirm sign up"
  type        = string
}

//define  auth challenge name
variable "auth_cognito_define_auth_challenge_function_name" {
  description = "lambda function name for cognito define auth challenge"
  type        = string
}

//post authentication name
variable "auth_cognito_post_authentication_function_name" {
  description = "lambda function name for cognito post authentication"
  type        = string
}

//ttop name
variable "auth_cognito_top_function_name" {
  description = "lambda function name for ttop secret"
  type        = string
}

//dynamodb table name
variable "dynamo_table_totp_name" {
  description = "The TOTP secrets"
  type        = string

}


//post confirmation name
variable "auth_cognito_post_confirmation_function_name" {
  description = "lambda function name for cognito post confirmation"
  type        = string
}

//pre authentication name
variable "auth_cognito_pre_authentication_function_name" {
  description = "lambda function name for cognito pre authentication"
  type        = string
}

//post verify auth challenge response name
variable "auth_cognito_verify_auth_challenge_response_function_name" {
  description = "lambda function name for cognito verify auth challenge response"
  type        = string
}

//create auth challenge function name
variable "auth_cognito_create_auth_challenge_function_name" {
  description = "lambda function name for cognito create auth challenge"
  type        = string
}

variable "table_name" {
  description = "The name of the DynamoDB table"
  type        = string

}

variable "dynamo_table_users_name" {
  description = "The name of the DynamoDB table"
  type        = string

}

variable "admin_user_pool_id" {
  description = "The ID of the Cognito User Pool"
  type        = string

}

variable "hash_key" {
  description = "The hash key of the DynamoDB table"
  type        = string

}

variable "range_key" {
  description = "The range key of the DynamoDB table"
  type        = string
}

variable "read_capacity" {
  description = "The read capacity of the DynamoDB table"
  type        = number
}

variable "write_capacity" {
  description = "The write capacity of the DynamoDB table"
  type        = number
}

variable "jwt_secret" {
  description = "The secret for the JWT"
  type        = string
  
}

variable "auth_cognito_sold_product_function_name" {
  description = "lambda function name for cognito sold product"
  type = string
}

variable "token_authorizer_lambda_function_name" {
  description = "lambda function name for token authorizer"
}

variable "dynamo_table_orders_name" {
  description = "Name of the DynamoDB Orders table"
  type        = string
}