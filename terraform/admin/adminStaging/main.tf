//admin/adminStaging/main.tf
provider "aws" {
  region = var.aws_region
}

module "admin_s3_hosting" {
  source              = "../modules/admin-s3-hosting"
  admin_bucket_name   = var.admin_bucket_name
  admin_domain_name   = var.root_domain
  zone_id             = var.zone_id
  admin_s3_sub_domain = var.admin_s3_sub_domain
  image_bucket_name   = var.image_bucket_name
}

//./main.tf
module "apigateway" {
  source                                              = "../modules/admin-api-gateway"
  aws_region                                          = var.aws_region
  api_gateway_account_id                              = var.api_gateway_account_id
  root_domain                                         = var.root_domain
  admin_sub_domain                                    = var.admin_sub_domain
  admin_api_gateway                                   = var.admin_api_gateway
  cognito_admin_user_pool_arn                         = module.admin_cognito.cognito_admin_user_pool_arn
  admin_user_pool_id                                  = var.admin_user_pool_id
  api_gateway_lambda_invoke_role_token_authorizer_arn = module.admin_lambda.lambda_execution_role_token_authorizer

  get_lambda_function_name = module.admin_lambda.get_lambda_function_name
  get_lambda_arn           = module.admin_lambda.get_lambda_arn

  presign_lambda_function_name = module.admin_lambda.presign_lambda_function_name
  presign_lambda_function_arn = module.admin_lambda.presign_lambda_function_arn

  get_product_by_id_lambda_function_name = module.admin_lambda.get_product_by_id_lambda_function_name
  get_product_by_id_lambda_arn           = module.admin_lambda.get_product_by_id_lambda_arn

  put_lambda_function_name = module.admin_lambda.put_lambda_function_name
  put_lambda_arn           = module.admin_lambda.put_lambda_arn

  post_lambda_function_name = module.admin_lambda.post_lambda_function_name
  post_lambda_arn           = module.admin_lambda.post_lambda_arn

  delete_lambda_function_name = module.admin_lambda.delete_lambda_function_name
  delete_lambda_arn           = module.admin_lambda.delete_lambda_arn

  auth_cognito_login_function_name = module.admin_lambda.auth_login_lambda_function_name
  auth_cognito_login_arn           = module.admin_lambda.auth_login_lambda_function_arn

  auth_cognito_logout_function_name = module.admin_lambda.auth_cognito_logout_function_name
  auth_cognito_logout_arn           = module.admin_lambda.auth_cognito_logout_function_arn

  auth_cognito_forgot_password_function_name = module.admin_lambda.auth_cognito_forgot_password_function_name
  auth_cognito_forgot_password_arn           = module.admin_lambda.auth_cognito_forgot_password_function_arn

  auth_cognito_resend_code_function_name = module.admin_lambda.auth_cognito_resend_code_function_name
  auth_cognito_resend_code_arn           = module.admin_lambda.auth_cognito_resend_code_function_arn

  auth_cognito_top_function_name = module.admin_lambda.auth_cognito_ttop_function_name
  auth_cognito_ttop_function_arn = module.admin_lambda.auth_cognito_ttop_function_arn

  auth_cognito_signup_function_name = module.admin_lambda.auth_cognito_signup_function_name
  auth_cognito_signup_function_arn  = module.admin_lambda.auth_cognito_signup_function_arn


  auth_cognito_post_confirmation_function_name = module.admin_lambda.auth_cognito_post_confirmation_function_name
  post_confirmation_lambda_arn                 = module.admin_lambda.auth_cognito_post_confirmation_function_arn

  auth_cognito_verify_mfa_function_name = module.admin_lambda.auth_cognito_verify_mfa_function_name
  auth_cognito_verify_mfa_lambda_arn    = module.admin_lambda.auth_cognito_verify_mfa_function_arn

  auth_cognito_confirm_signup_function_name = module.admin_lambda.auth_cognito_confirm_signup_function_name
  auth_cognito_confirm_signup_function_arn  = module.admin_lambda.auth_cognito_confirm_signup_function_arn

  auth_cognito_refresh_token_arn           = module.admin_lambda.auth_cognito_refresh_token_arn
  auth_cognito_refresh_token_function_name = module.admin_lambda.auth_cognito_refresh_token_function_name

  auth_cognito_sold_product_function_name = module.admin_lambda.auth_cognito_sold_product_function_name
  auth_cognito_sold_product_lambda_arn    = module.admin_lambda.auth_cognito_sold_product_lambda_arn

  authorizer_lambda_function_name = module.admin_lambda.authorizer_lambda_function_name
  authorizer_lambda_function_arn  = module.admin_lambda.authorizer_lambda_function_arn

  lambda_execution_role_token_authorizer = module.admin_lambda.lambda_execution_role_token_authorizer
}



module "admin_cognito" {
  source         = "../modules/admin-cognito"
  aws_region     = var.aws_region
  aws_account_id = var.aws_account_id
  sns_topic_name = var.sns_topic_name
  zone_id        = var.zone_id

  pre_sign_up_lambda_arn                                   = module.admin_lambda.auth_cognito_pre_signup_function_arn
  auth_cognito_pre_signup_function_arn                     = module.admin_lambda.auth_cognito_pre_signup_function_arn
  auth_cognito_pre_authentication_function_arn             = module.admin_lambda.auth_cognito_pre_authentication_function_arn
  auth_cognito_post_authentication_function_arn            = module.admin_lambda.auth_cognito_post_authentication_function_arn
  auth_cognito_post_confirmation_function_name             = module.admin_lambda.auth_cognito_post_confirmation_function_name
  auth_cognito_post_confirmation_function_arn              = module.admin_lambda.auth_cognito_post_confirmation_function_arn
  auth_cognito_verify_auth_challenge_response_function_arn = module.admin_lambda.auth_cognito_verify_auth_challenge_response_function_arn
  auth_cognito_create_auth_challenge_function_arn          = module.admin_lambda.auth_cognito_create_auth_challenge_function_arn
  auth_cognito_define_auth_challenge_function_arn          = module.admin_lambda.auth_cognito_define_auth_challenge_function_arn

  dynamo_table_users_name = var.dynamo_table_users_name


  auth_cognito_pre_signup_function_name         = module.admin_lambda.auth_cognito_pre_signup_function_name
  auth_cognito_pre_authentication_function_name = module.admin_lambda.auth_cognito_pre_authentication_function_name

  auth_cognito_define_auth_challenge_function_name = module.admin_lambda.auth_cognito_define_auth_challenge_function_name
  auth_cognito_post_authentication_function_name   = module.admin_lambda.auth_cognito_post_authentication_function_name

  auth_cognito_verify_auth_challenge_response_function_name = module.admin_lambda.auth_cognito_verify_auth_challenge_response_function_name
  auth_cognito_create_auth_challenge_function_name          = module.admin_lambda.auth_cognito_create_auth_challenge_function_name
}

module "dynamodb" {
  source     = "../modules/dynamodb"
  aws_region = var.aws_region

  table_name              = var.table_name
  hash_key                = var.hash_key
  range_key               = var.range_key
  read_capacity           = var.read_capacity
  write_capacity          = var.write_capacity
  dynamo_table_users_name = var.dynamo_table_users_name
  aws_sns_topic_name      = var.sns_topic_name
  dynamo_table_totp_name  = var.dynamo_table_totp_name
  dynamo_table_orders_name = var.dynamo_table_orders_name
}

module "admin_lambda" {
  source         = "../modules/admin-lambda"
  aws_region     = var.aws_region
  aws_account_id = var.aws_account_id
  s3_bucket      = var.s3_bucket
  client_id      = var.client_id
  client_secret  = var.client_secret
  ses_sender     = var.ses_sender

  admin_api_gateway_id = module.apigateway.admin_api_gateway_id

  get_lambda_function_name    = var.get_lambda_function_name
  get_product_by_id_lambda_function_name = var.get_product_by_id_lambda_function_name
  put_lambda_function_name    = var.put_lambda_function_name
  post_lambda_function_name   = var.post_lambda_function_name
  delete_lambda_function_name = var.delete_lambda_function_name
  resizer_lambda_function_name = var.resizer_lambda_function_name
  presign_lambda_function_name = var.presign_lambda_function_name

  auth_cognito_login_function_name           = var.auth_cognito_login_function_name
  auth_cognito_logout_function_name          = var.auth_cognito_logout_function_name
  auth_cognito_forgot_password_function_name = var.auth_cognito_forgot_password_function_name
  auth_cognito_resend_code_function_name     = var.auth_cognito_resend_code_function_name

  //Auth for MFA
  auth_cognito_pre_signup_function_name = var.auth_cognito_pre_signup_function_name
  auth_cognito_signup_function_name     = var.auth_cognito_signup_function_name

  auth_cognito_confirm_signup_function_name = var.auth_cognito_confirm_signup_function_name

  auth_cognito_define_auth_challenge_function_name          = var.auth_cognito_define_auth_challenge_function_name
  auth_cognito_post_authentication_function_name            = var.auth_cognito_post_authentication_function_name
  auth_cognito_post_confirmation_function_name              = var.auth_cognito_post_confirmation_function_name
  auth_cognito_pre_authentication_function_name             = var.auth_cognito_pre_authentication_function_name
  auth_cognito_verify_auth_challenge_response_function_name = var.auth_cognito_verify_auth_challenge_response_function_name
  auth_cognito_create_auth_challenge_function_name          = var.auth_cognito_create_auth_challenge_function_name
  auth_cognito_top_function_name                            = var.auth_cognito_top_function_name

  auth_cognito_verify_mfa_function_name = var.auth_cognito_verify_mfa_function_name

  auth_cognito_sold_product_function_name = var.auth_cognito_sold_product_function_name


  auth_cognito_refresh_token_function_name = var.auth_cognito_refresh_token_function_name

  token_authorizer_lambda_function_name = var.token_authorizer_lambda_function_name

  api_gateway_account_id = var.api_gateway_account_id
  admin_api_gateway_arn  = module.apigateway.admin_api_gateway_arn
  admin_user_pool_id     = var.admin_user_pool_id

  dynamo_table_users_name    = module.dynamodb.dynamo_table_users_name
  dynamo_table_products_name = var.dynamo_table_products_name
  dynamo_table_totp_name     = module.dynamodb.ttop_secrets

  sns_topic_name = module.admin_cognito.sns_topic_name

  jwt_secret = var.jwt_secret
  image_bucket_name = var.image_bucket_name
}


