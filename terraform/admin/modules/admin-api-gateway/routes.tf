//./modules/admin-api-gateway/routes.tf
resource "aws_api_gateway_resource" "admin_rest_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "products"
}

resource "aws_api_gateway_resource" "admin_rest_api_resource_list" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "admin-list-products"
}

# Add a resource for /products/{id}
resource "aws_api_gateway_resource" "admin_rest_api_product_id_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_resource.admin_rest_api_resource.id
  path_part   = "{id}" # Represents the product ID in the path
}


//GET METHOD
//---------------------------------------------------------
resource "aws_api_gateway_method" "admin_rest_api_get_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_resource_list.id
  http_method   = "GET"
  authorization = "NONE"
  # authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

  # request_parameters = {
  #   "method.request.header.Authorization" = true
  # }
}

resource "aws_api_gateway_method_response" "rest_api_get_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource_list.id
  http_method = aws_api_gateway_method.admin_rest_api_get_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "rest_api_get_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_resource_list.id
  http_method             = aws_api_gateway_method.admin_rest_api_get_method.http_method
  integration_http_method = "POST" # Use POST for Lambda integration
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.get_lambda_arn}/invocations"
}


resource "aws_api_gateway_integration_response" "rest_api_get_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource_list.id
  http_method = aws_api_gateway_integration.rest_api_get_method_integration.http_method
  status_code = aws_api_gateway_method_response.rest_api_get_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET,POST,PUT,DELETE'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "get_api_gateway_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.get_lambda_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.get_lambda_arn
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/*/admin-list-products"

  lifecycle {
    ignore_changes = [statement_id]
  }
}


//PUT METHOD
//---------------------------------------------------------
resource "aws_api_gateway_method" "admin_rest_api_put_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method   = "PUT"
  authorization = "NONE"
  # authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id
}

resource "aws_api_gateway_method_response" "rest_api_put_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_put_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "rest_api_put_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method             = aws_api_gateway_method.admin_rest_api_put_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.put_lambda_arn}/invocations"

}

resource "aws_api_gateway_integration_response" "rest_api_put_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_integration.rest_api_put_method_integration.http_method
  status_code = aws_api_gateway_method_response.rest_api_put_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET,POST,PUT,DELETE'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "put_api_gateway_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.put_lambda_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.put_lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.admin_rest_api_put_method.http_method}${aws_api_gateway_resource.admin_rest_api_product_id_resource.path}"

  lifecycle {
    ignore_changes = [function_name, statement_id]
  }
}


//POST METHOD
//---------------------------------------------------------
resource "aws_api_gateway_method" "admin_rest_api_post_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method   = "POST"
  authorization = "NONE"
  # authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id


  # request_parameters = {
  #   "method.request.header.Authorization" = true

  # }
}


resource "aws_api_gateway_integration" "rest_api_post_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method             = aws_api_gateway_method.admin_rest_api_post_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.post_lambda_arn}/invocations"

  # request_parameters = {
  #   "integration.request.header.Authorization" = "method.request.header.Authorization"
  # }


}




resource "aws_lambda_permission" "post_api_gateway_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.post_lambda_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.post_lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/*/products"

  lifecycle {
    ignore_changes = [function_name, statement_id]
  }
}


//DELETE METHOD
//---------------------------------------------------------
resource "aws_api_gateway_method" "admin_rest_api_delete_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method   = "DELETE"
  authorization = "NONE"
  # authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

  request_parameters = {
    "method.request.path.id" = true # Declare the path parameter requirement
  }
}

resource "aws_api_gateway_method_response" "rest_api_delete_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_delete_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "rest_api_delete_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method             = aws_api_gateway_method.admin_rest_api_delete_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.delete_lambda_arn}/invocations"

  request_parameters = {
    "integration.request.path.id" = "method.request.path.id" # Map path parameter
  }
}

resource "aws_api_gateway_integration_response" "rest_api_delete_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_integration.rest_api_delete_method_integration.http_method
  status_code = aws_api_gateway_method_response.rest_api_get_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET,POST,PUT,DELETE'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }



}

resource "aws_lambda_permission" "delete_api_gateway_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.delete_lambda_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.delete_lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/DELETE/products/{id}"

  lifecycle {
    ignore_changes = [statement_id]
  }
}


# GET METHOD for /products/{id}
resource "aws_api_gateway_method" "admin_rest_api_get_product_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method   = "GET"
  authorization = "NONE" # Adjust if using authorization
  request_parameters = {
    "method.request.path.id" = true # Declare the path parameter requirement
  }
}

# Method Response for GET /products/{id}
resource "aws_api_gateway_method_response" "rest_api_get_product_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_get_product_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

# Integration for GET /products/{id}
resource "aws_api_gateway_integration" "rest_api_get_product_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method             = aws_api_gateway_method.admin_rest_api_get_product_method.http_method
  integration_http_method = "POST" # AWS_PROXY requires POST
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.get_product_by_id_lambda_arn}/invocations"
}

# Integration Response for GET /products/{id}
resource "aws_api_gateway_integration_response" "rest_api_get_product_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_product_id_resource.id
  http_method = aws_api_gateway_integration.rest_api_get_product_method_integration.http_method
  status_code = aws_api_gateway_method_response.rest_api_get_product_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET,POST,PUT,DELETE'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

# Lambda Permission for GET /products/{id}
resource "aws_lambda_permission" "get_product_api_gateway_lambda" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.get_product_by_id_lambda_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.get_product_by_id_lambda_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/GET/products/*"

  lifecycle {
    ignore_changes = [statement_id]
  }
}


#CORS




// OPTIONS METHOD
resource "aws_api_gateway_method" "admin_rest_api_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method_response" "rest_api_options_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_options_method.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "rest_api_options_method_integration" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_options_method.http_method
  type        = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}


resource "aws_api_gateway_integration_response" "rest_api_options_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource.id
  http_method = aws_api_gateway_method.admin_rest_api_options_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET,POST,PUT,DELETE'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
  response_templates = {
    "application/json" = ""
  }

  depends_on = [
    aws_api_gateway_integration.rest_api_options_method_integration
  ]
}


############################
# API Gateway Setup
############################

resource "aws_api_gateway_resource" "admin_rest_api_resource_presigned_url" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "presigned-url"
}

resource "aws_api_gateway_method" "presigned_url_post" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "presigned_url_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method             = aws_api_gateway_method.presigned_url_post.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"

  uri = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.presign_lambda_function_arn}/invocations"
}

resource "aws_api_gateway_deployment" "presigned_url_api_deployment" {
  depends_on = [
    aws_api_gateway_integration.presigned_url_post_integration
  ]

  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  stage_name  = "dev"
}

resource "aws_lambda_permission" "allow_api_gateway_invoke_presigned_url" {
  statement_id  = "AllowAPIGatewayInvokePresignedURL"
  action        = "lambda:InvokeFunction"
  function_name = var.presign_lambda_function_name
  principal     = "apigateway.amazonaws.com"

  source_arn = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/POST/presigned-url"

}


//CORS

# 4. OPTIONS Method for /presigned-url (CORS Preflight)
resource "aws_api_gateway_method" "presigned_url_options" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# 5. Integration for OPTIONS /presigned-url (Mock Integration)
resource "aws_api_gateway_integration" "presigned_url_options" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method             = aws_api_gateway_method.presigned_url_options.http_method
  integration_http_method = "OPTIONS"
  type                    = "MOCK"

  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }

  passthrough_behavior = "WHEN_NO_MATCH"
}

# 6. Method Response for OPTIONS (CORS Headers)
resource "aws_api_gateway_method_response" "presigned_url_options_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method = aws_api_gateway_method.presigned_url_options.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Headers" = true
  }
}

# 7. Integration Response for OPTIONS (CORS Headers)
resource "aws_api_gateway_integration_response" "presigned_url_options_200" {
  rest_api_id       = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id       = aws_api_gateway_resource.admin_rest_api_resource_presigned_url.id
  http_method       = aws_api_gateway_method.presigned_url_options.http_method
  status_code       = aws_api_gateway_method_response.presigned_url_options_200.status_code
  response_templates = {
    "application/json" = ""
  }

  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = "'https://admin.thealpinestudio.com'"  # Replace with your frontend URL or "*" for all
    "method.response.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,Authorization'"
  }
}
