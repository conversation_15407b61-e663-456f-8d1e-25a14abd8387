//./modules/admin-api-gateway/main.tf
data "aws_region" "current" {}

data "aws_caller_identity" "current" {}

resource "aws_api_gateway_rest_api" "admin_api_gateway" {
  name = var.admin_api_gateway
  endpoint_configuration {
    types = ["REGIONAL"]
  }
}

# resource "aws_api_gateway_authorizer" "admin_api_authorizer" {
#   name        = "LambdaTokenAuthorizer"
#   type        = "TOKEN"
#   rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
#   # Corrected authorizer_uri with full ARN of the Lambda function
#   authorizer_uri   = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.authorizer_lambda_function_arn}/invocations"

#   # Identity source - where to extract the token (Authorization header)
#   identity_source = "method.request.header.Authorization"

#   # Optional: Specify an IAM role with permission to invoke the Lambda authorizer
#   authorizer_credentials = var.api_gateway_lambda_invoke_role_token_authorizer_arn
#   # Optional: Add token validation regex (recommended for Cognito)
#   # token_validation = "^Bearer [A-Za-z0-9\-_=]+\.[A-Za-z0-9\-_=]+\.[A-Za-z0-9\-_=]+$"
# }

resource "aws_api_gateway_authorizer" "admin_api_authorizer" {
  name                             = "LambdaTokenAuthorizer"
  type                             = "TOKEN"
  rest_api_id                      = aws_api_gateway_rest_api.admin_api_gateway.id
  authorizer_uri                   = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.authorizer_lambda_function_arn}/invocations"
  identity_source                  = "method.request.header.Authorization"
  authorizer_credentials           = var.api_gateway_lambda_invoke_role_token_authorizer_arn
  authorizer_result_ttl_in_seconds = 300
}



resource "aws_api_gateway_deployment" "rest_api_deployment" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  stage_name  = "dev"

  triggers = {
    redeployment = sha1(jsonencode([
      aws_api_gateway_integration.rest_api_get_method_integration.id,
      aws_api_gateway_integration.rest_api_put_method_integration.id,
      aws_api_gateway_integration.rest_api_post_method_integration.id,
      aws_api_gateway_integration.rest_api_delete_method_integration.id,
      aws_api_gateway_integration.rest_api_get_product_method_integration.id,
      // Removed reference to aws_api_gateway_integration_response.rest_api_post_method_integration_response_200.id
    ]))
  }

  depends_on = [
    aws_api_gateway_method.admin_rest_api_get_method,
    aws_api_gateway_method.admin_rest_api_post_method,
    aws_api_gateway_method.admin_rest_api_put_method,
    aws_api_gateway_method.admin_rest_api_delete_method,

    aws_api_gateway_integration.rest_api_get_method_integration,
    aws_api_gateway_integration.rest_api_put_method_integration,
    aws_api_gateway_integration.rest_api_post_method_integration,
    aws_api_gateway_integration.rest_api_delete_method_integration,
    aws_api_gateway_integration.rest_api_get_product_method_integration,

    // Removed reference to aws_api_gateway_integration_response.rest_api_post_method_integration_response_200
  ]

  lifecycle {
    create_before_destroy = true
  }
}





resource "aws_api_gateway_stage" "dev" {
  deployment_id = aws_api_gateway_deployment.rest_api_deployment.id
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  stage_name    = "dev"
  variables = {
    "COGNITO_USER_POOL_ARN" = var.cognito_admin_user_pool_arn
  }

  depends_on = [
    aws_api_gateway_deployment.rest_api_deployment,
    aws_api_gateway_integration.rest_api_post_method_integration,
    aws_api_gateway_integration.rest_api_get_method_integration,
    aws_api_gateway_integration.rest_api_put_method_integration,
    aws_api_gateway_integration.rest_api_delete_method_integration,
    aws_api_gateway_integration.rest_api_get_product_method_integration,
    


  ]
}





#Cloudwatch for API
#---------------------------------------------------------
resource "aws_iam_role" "admin_api_gateway_cloudwatch_logs" {
  name = "admin_api_gateway_cloudwatch_logs"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "apigateway.amazonaws.com"
        }
      }
    ]
  })
}

resource "aws_iam_role_policy" "admin_api_gateway_cloudwatch_logs" {
  name = "admin_api_gateway_cloudwatch_logs"
  role = aws_iam_role.admin_api_gateway_cloudwatch_logs.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:PutLogEvents",
          "logs:GetLogEvents",
          "logs:FilterLogEvents"
        ]
        Effect   = "Allow"
        Resource = "*"
      }
    ]
  })
}

resource "aws_api_gateway_account" "rest_api_account" {
  cloudwatch_role_arn = aws_iam_role.admin_api_gateway_cloudwatch_logs.arn
}

resource "aws_cloudwatch_log_group" "cloudwatch_api" {
  name = "cloudwatch_admin_api"


  #   access_log_settings {
  #     destination_arn = aws_cloudwatch_log_group.cloudwatch_api.arn

  #     format = jsonencode({
  #       requestId               = "$context.requestId"
  #       sourceIp                = "$context.identity.sourceIp"
  #       requestTime             = "$context.requestTime"
  #       protocol                = "$context.protocol"
  #       httpMethod              = "$context.httpMethod"
  #       resourcePAth            = "$context.resourcePath"
  #       routeKey                = "$context.routeKey"
  #       status                  = "$context.status"
  #       responseLength          = "$context.responseLength"
  #       integrationErrorMessage = "context.$integrationErrorMessage"
  #       }
  #     )
  #   }
}

//API GATEWAY DOMAIN
//---------------------------------------------------------------------------------
resource "aws_api_gateway_domain_name" "api_domain" {
  domain_name              = var.admin_sub_domain
  regional_certificate_arn = aws_acm_certificate_validation.admin_api_certificate_validation.certificate_arn

  endpoint_configuration {
    types = ["REGIONAL"]
  }

  depends_on = [aws_acm_certificate_validation.admin_api_certificate_validation]
}

# Example DNS record using Route53.
# Route53 is not specifically required; any DNS host can be used.
resource "aws_route53_record" "api_record" {
  name    = aws_api_gateway_domain_name.api_domain.domain_name
  type    = "A"
  zone_id = data.aws_route53_zone.zone.id

  alias {
    evaluate_target_health = true
    name                   = aws_api_gateway_domain_name.api_domain.regional_domain_name
    zone_id                = aws_api_gateway_domain_name.api_domain.regional_zone_id
  }
}

//HANDLING ERRORS
//---------------------------------------------------------------------------------
resource "aws_api_gateway_gateway_response" "response_4xx" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  response_type = "DEFAULT_4XX"
  response_templates = {
    "application/json" = "{'message':$context.error.messageString}"
  }
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "gatewayresponse.header.Access-Control-Allow-Methods"     = "'GET, POST, OPTIONS, PUT, DELETE'"
    "gatewayresponse.header.Access-Control-Allow-Headers"     = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "gatewayresponse.header.Access-Control-Allow-Credentials" = "'true'"
  }
}
resource "aws_api_gateway_gateway_response" "response_5xx" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  response_type = "DEFAULT_5XX"
  response_templates = {
    "application/json" = "{'message':$context.error.messageString}"
  }
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "gatewayresponse.header.Access-Control-Allow-Methods"     = "'GET, POST, OPTIONS, PUT, DELETE'"
    "gatewayresponse.header.Access-Control-Allow-Headers"     = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "gatewayresponse.header.Access-Control-Allow-Credentials" = "'true'"

  }
}
