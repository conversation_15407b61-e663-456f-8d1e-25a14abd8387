//module/admin-api-gateway/routes.tf
//AUTH COGNITO
//GET METHOD && POST METHOD
//---------------------------------------------------------
//9 Define the resource for login authentication
resource "aws_api_gateway_resource" "auth_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "auth-login"
}

resource "aws_api_gateway_method" "auth_login_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.auth_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "auth__login_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.auth_resource.id
  http_method = aws_api_gateway_method.auth_login_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "auth_login_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.auth_resource.id
  http_method             = aws_api_gateway_method.auth_login_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_login_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.auth_login_method
  ]
}

resource "aws_api_gateway_integration_response" "auth_login_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.auth_resource.id
  http_method = aws_api_gateway_integration.auth_login_integration.http_method
  status_code = aws_api_gateway_method_response.auth__login_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'*'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "auth_login_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-auth_login"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_login_arn
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.auth_login_method.http_method}${aws_api_gateway_resource.auth_resource.path}"
}



//LOGOUT ROUTE
resource "aws_api_gateway_resource" "logout_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "logout"
}

resource "aws_api_gateway_method" "auth_logout_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.logout_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "logout_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.logout_resource.id
  http_method = aws_api_gateway_method.auth_logout_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "auth_logout_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.logout_resource.id
  http_method             = aws_api_gateway_method.auth_logout_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_logout_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.auth_logout_method
  ]
}

resource "aws_api_gateway_integration_response" "logout_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.logout_resource.id
  http_method = aws_api_gateway_integration.auth_logout_integration.http_method
  status_code = aws_api_gateway_method_response.logout_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'*'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "auth_logout_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-auth_logout"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_logout_arn
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.auth_logout_method.http_method}${aws_api_gateway_resource.logout_resource.path}"
}

//REFRESHTOKEN ROUTE
resource "aws_api_gateway_resource" "refresh_token_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "refresh-token"
}

resource "aws_api_gateway_method" "auth_refresh_token_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.refresh_token_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "refresh_token_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.refresh_token_resource.id
  http_method = aws_api_gateway_method.auth_refresh_token_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "auth_refresh_token_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.refresh_token_resource.id
  http_method             = aws_api_gateway_method.auth_refresh_token_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_refresh_token_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.auth_refresh_token_method
  ]
}

resource "aws_api_gateway_integration_response" "refresh_token_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.refresh_token_resource.id
  http_method = aws_api_gateway_integration.auth_refresh_token_integration.http_method
  status_code = aws_api_gateway_method_response.refresh_token_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'*'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "auth_refresh_token_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-auth_refresh_token"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_refresh_token_arn
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.auth_refresh_token_method.http_method}${aws_api_gateway_resource.refresh_token_resource.path}"
}


//7 FORGOT PASSWORD ROUTE
resource "aws_api_gateway_resource" "forgot_password_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "forgot-password"

}

resource "aws_api_gateway_method" "auth_forgot_password_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.forgot_password_resource.id
  http_method   = "POST"
  authorization = "NONE"

}

resource "aws_api_gateway_method_response" "forgot_password_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.forgot_password_resource.id
  http_method = "POST"
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "auth_forgot_password_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.forgot_password_resource.id
  http_method             = aws_api_gateway_method.auth_forgot_password_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_forgot_password_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.auth_forgot_password_method
  ]
}

resource "aws_api_gateway_integration_response" "forgot_password_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.forgot_password_resource.id
  http_method = aws_api_gateway_integration.auth_forgot_password_integration.http_method
  status_code = aws_api_gateway_method_response.forgot_password_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'*'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "auth_forgot_password_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_forgot_password_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_forgot_password_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.auth_forgot_password_method.http_method}${aws_api_gateway_resource.forgot_password_resource.path}"
}



//RESEND CODE ROUTE
resource "aws_api_gateway_resource" "resend_code_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "resend-code"
}

resource "aws_api_gateway_method" "auth_resend_code_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.resend_code_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "resend_code_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.resend_code_resource.id
  http_method = aws_api_gateway_method.auth_resend_code_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "auth_resend_code_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.resend_code_resource.id
  http_method             = aws_api_gateway_method.auth_resend_code_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_resend_code_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.auth_resend_code_method
  ]
}

resource "aws_api_gateway_integration_response" "resend_code_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.resend_code_resource.id
  http_method = aws_api_gateway_integration.auth_resend_code_integration.http_method
  status_code = aws_api_gateway_method_response.resend_code_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'*'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "auth_resend_code_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_resend_code_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_resend_code_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.auth_resend_code_method.http_method}${aws_api_gateway_resource.resend_code_resource.path}"
}



//SIGN LAMBDA ROUTE
resource "aws_api_gateway_resource" "sign_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "signup"

}

resource "aws_api_gateway_method" "sign_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.sign_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "sign_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.sign_resource.id
  http_method = aws_api_gateway_method.sign_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "sign_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.sign_resource.id
  http_method             = aws_api_gateway_method.sign_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_signup_function_arn}/invocations"


  request_templates = {
    "application/json" = <<EOF
{
  "body": "$input.body",
  "headers": {
    #foreach($param in $input.params().header.keySet())
    "$param": "$util.escapeJavaScript($input.params().header.get($param))"
    #if($foreach.hasNext),#end
    #end
  },
  "httpMethod": "$context.httpMethod",
  "isBase64Encoded": "$context.isBase64Encoded",
  "path": "$context.resourcePath",
  "resource": "$context.resourcePath"
}
EOF
  }

  depends_on = [
    aws_api_gateway_method.sign_method
  ]
}

resource "aws_api_gateway_integration_response" "sign_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.sign_resource.id
  http_method = aws_api_gateway_integration.sign_integration.http_method
  status_code = aws_api_gateway_method_response.sign_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealipinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "sign_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_signup_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.sign_method.http_method}${aws_api_gateway_resource.sign_resource.path}"
}


//CONFIRM SIGNUP LAMBDA ROUTE
resource "aws_api_gateway_resource" "confirm_signup_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "confirm-signup"
}

resource "aws_api_gateway_method" "confirm_signup_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.confirm_signup_resource.id
  http_method   = "POST"
  authorization = "NONE"
}

resource "aws_api_gateway_method_response" "confirm_signup_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.confirm_signup_resource.id
  http_method = aws_api_gateway_method.confirm_signup_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "confirm_signup_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.confirm_signup_resource.id
  http_method             = aws_api_gateway_method.confirm_signup_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_confirm_signup_function_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.confirm_signup_method
  ]
}

resource "aws_api_gateway_integration_response" "confirm_signup_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.confirm_signup_resource.id
  http_method = aws_api_gateway_integration.confirm_signup_integration.http_method
  status_code = aws_api_gateway_method_response.confirm_signup_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST, OPTIONS'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "confirm_signup_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_confirm_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_confirm_signup_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.confirm_signup_method.http_method}${aws_api_gateway_resource.confirm_signup_resource.path}"
}

resource "aws_api_gateway_method" "confirm_signup_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.confirm_signup_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_method_response" "confirm_signup_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.confirm_signup_resource.id
  http_method = aws_api_gateway_method.confirm_signup_options_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "confirm_signup_options_integration" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.confirm_signup_resource.id
  http_method = aws_api_gateway_method.confirm_signup_options_method.http_method
  type        = "MOCK"
  request_templates = {
    "application/json" = "{\"statusCode\": 200}"
  }
}

resource "aws_api_gateway_integration_response" "confirm_signup_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.confirm_signup_resource.id
  http_method = aws_api_gateway_integration.confirm_signup_options_integration.http_method
  status_code = aws_api_gateway_method_response.confirm_signup_options_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST, OPTIONS'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}


//POST CONFIRMATION LAMBDA ROUTE
resource "aws_api_gateway_resource" "post_confirmation_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "post-confirmation"

}

resource "aws_api_gateway_method" "post_confirmation_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.post_confirmation_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "post_confirmation_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.post_confirmation_resource.id
  http_method = aws_api_gateway_method.post_confirmation_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "post_confirmation_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.post_confirmation_resource.id
  http_method             = aws_api_gateway_method.post_confirmation_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.post_confirmation_lambda_arn}/invocations"

  request_templates = {
    "application/json" = <<EOF
{
  "version": "1",
  "triggerSource": "PostConfirmation_ConfirmSignUp",
  "region": "$context.region",
  "userPoolId": "$input.path('$.userPoolId')",
  "userName": "$input.path('$.userName')",
  "callerContext": {
    "awsSdkVersion": "$context.identity.caller"
  },
  "request": {
    "userAttributes": {
      "email": "$input.path('$.request.userAttributes.email')",
      "phone_number": "$input.path('$.request.userAttributes.phone_number')",
      "name": "$input.path('$.request.userAttributes.name')"
    }
  },
  "response": {}
}
EOF
  }

  depends_on = [
    aws_api_gateway_method.post_confirmation_method,
    aws_lambda_permission.post_confirmation_lambda_permissions
  ]

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_api_gateway_integration_response" "post_confirmation_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.post_confirmation_resource.id
  http_method = aws_api_gateway_integration.post_confirmation_integration.http_method
  status_code = aws_api_gateway_method_response.post_confirmation_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealipinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }


}


resource "aws_lambda_permission" "post_confirmation_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_post_confirmation_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_post_confirmation_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.post_confirmation_method.http_method}${aws_api_gateway_resource.post_confirmation_resource.path}"
}



//VERIFY MFA
resource "aws_api_gateway_resource" "verify_mfa_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "verify-mfa"

}

resource "aws_api_gateway_method" "verify_mfa_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.verify_mfa_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "verify_mfa_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.verify_mfa_resource.id
  http_method = aws_api_gateway_method.verify_mfa_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "verify_mfa_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.verify_mfa_resource.id
  http_method             = aws_api_gateway_method.verify_mfa_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_verify_mfa_lambda_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.verify_mfa_method
  ]
}

resource "aws_api_gateway_integration_response" "verify_mfa_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.verify_mfa_resource.id
  http_method = aws_api_gateway_integration.verify_mfa_integration.http_method
  status_code = aws_api_gateway_method_response.verify_mfa_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealipinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "verify_mfa_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_verify_mfa_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_verify_mfa_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.verify_mfa_method.http_method}${aws_api_gateway_resource.verify_mfa_resource.path}"
}

//TTOP ROUTE
resource "aws_api_gateway_resource" "top_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "totp-secret"

}

resource "aws_api_gateway_method" "top_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.top_resource.id
  http_method   = "POST"
  authorization = "NONE"
  authorizer_id = aws_api_gateway_authorizer.admin_api_authorizer.id

}

resource "aws_api_gateway_method_response" "top_method_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.top_resource.id
  http_method = aws_api_gateway_method.top_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration" "top_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.top_resource.id
  http_method             = aws_api_gateway_method.top_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_ttop_function_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.top_method
  ]
}

resource "aws_api_gateway_integration_response" "top_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.top_resource.id
  http_method = aws_api_gateway_integration.top_integration.http_method
  status_code = aws_api_gateway_method_response.top_method_response.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST'"
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "top_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_top_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_top_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/${aws_api_gateway_method.top_method.http_method}${aws_api_gateway_resource.top_resource.path}"
}


//SoldProduct ROUTE
// Define the /sold-product resource
resource "aws_api_gateway_resource" "sold_product_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_rest_api.admin_api_gateway.root_resource_id
  path_part   = "sold-product"
}

// Define the /sold-product/{id} resource
resource "aws_api_gateway_resource" "sold_product_id_resource" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  parent_id   = aws_api_gateway_resource.sold_product_resource.id
  path_part   = "{id}"
}

// POST Method for /sold-product/{id}
resource "aws_api_gateway_method" "sold_product_id_post_method" {
  rest_api_id   = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id   = aws_api_gateway_resource.sold_product_id_resource.id
  http_method   = "POST"
  authorization = "NONE" # Adjust if using authorization
}

// Integration for POST /sold-product/{id}
resource "aws_api_gateway_integration" "sold_product_id_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id             = aws_api_gateway_resource.sold_product_id_resource.id
  http_method             = aws_api_gateway_method.sold_product_id_post_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.aws_region}:lambda:path/2015-03-31/functions/${var.auth_cognito_sold_product_lambda_arn}/invocations"

  depends_on = [
    aws_api_gateway_method.sold_product_id_post_method
  ]
}

// OPTIONS Method for /sold-product/{id}
resource "aws_api_gateway_method" "sold_product_id_options_method" {
  rest_api_id      = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id      = aws_api_gateway_resource.sold_product_id_resource.id
  http_method      = "OPTIONS"
  authorization    = "NONE"
  api_key_required = false # Explicitly disable API key requirement

}


// Method Response for OPTIONS /sold-product/{id}
resource "aws_api_gateway_method_response" "sold_product_id_options_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.sold_product_id_resource.id
  http_method = aws_api_gateway_method.sold_product_id_options_method.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
    "method.response.header.Content-Type"                     = true # Added Content-Type

  }
}

// Integration for OPTIONS /sold-product/{id}
resource "aws_api_gateway_integration" "sold_product_id_options_integration" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.sold_product_id_resource.id
  http_method = aws_api_gateway_method.sold_product_id_options_method.http_method

  type                    = "MOCK"
  integration_http_method = "OPTIONS"

  request_templates = {
    "application/json" = "{\"statusCode\":200}"
  }
}

// Integration Response for OPTIONS /sold-product/{id}
resource "aws_api_gateway_integration_response" "sold_product_id_options_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  resource_id = aws_api_gateway_resource.sold_product_id_resource.id
  http_method = aws_api_gateway_method.sold_product_id_options_method.http_method
  status_code = "200"

  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,Authorization'"
    "method.response.header.Access-Control-Allow-Methods"     = "'POST,OPTIONS'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://admin.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
    "method.response.header.Content-Type"                     = "'application/json'"
  }

  response_templates = {
    "application/json" = "{}" # Return an empty JSON object
  }

  depends_on = [
    aws_api_gateway_integration.sold_product_id_options_integration
  ]
}


// Lambda Permission for POST /sold-product/{id}
resource "aws_lambda_permission" "sold_product_lambda_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.auth_cognito_sold_product_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_sold_product_function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.admin_api_gateway.id}/*/POST/sold-product/*"
}

// Deployment
resource "aws_api_gateway_deployment" "admin_api_deployment" {
  depends_on = [
    aws_api_gateway_integration.sold_product_id_post_integration,
    aws_api_gateway_integration.sold_product_id_options_integration,
    aws_api_gateway_integration_response.sold_product_id_options_integration_response_200,
    # Remove or correct any undefined dependencies
  ]



  rest_api_id = aws_api_gateway_rest_api.admin_api_gateway.id
  stage_name  = "dev"
}
