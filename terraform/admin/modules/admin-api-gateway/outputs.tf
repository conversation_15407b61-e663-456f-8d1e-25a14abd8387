//./modules/admin-api-gateway/outputs.tf
output "admin_api_gateway_arn" {
    value = aws_api_gateway_rest_api.admin_api_gateway.arn
    description = "ARN of the Admin API Gateway"
}

output "admin_api_gateway_id" {
    value = aws_api_gateway_rest_api.admin_api_gateway.id
    description = "ID of the Admin API Gateway"
  
}
output "api_gateway_invoke_url" {
  value       = "https://${aws_api_gateway_rest_api.admin_api_gateway.id}.execute-api.${data.aws_region.current.name}.amazonaws.com/${aws_api_gateway_stage.dev.stage_name}"
  description = "The URL endpoint for the deployed API Gateway"
}
