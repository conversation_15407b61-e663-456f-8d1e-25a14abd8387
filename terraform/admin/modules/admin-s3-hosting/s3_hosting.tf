provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

locals {
  s3_origin_id = "Admin thealpinestudio Origin Id"
}

#S3 BUCKET for WEBSITE
resource "aws_s3_bucket" "s3_admin_thealpinestudio_bucket" {
  bucket = var.admin_bucket_name

  lifecycle {
    ignore_changes = [bucket]
  }
}
#WEBSITE CONFIG
resource "aws_s3_bucket_website_configuration" "s3_admin_thealpinestudio_website_bucket" {
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }

  lifecycle {
    ignore_changes = [
      bucket

    ]
  }
}

resource "aws_s3_bucket_acl" "s3_admin_thealpinestudio_website_bucket_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.s3_admin_thealpinestudio_ownership,
    aws_s3_bucket_public_access_block.s3_admin_thealpinestudio_public_block,
  ]
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id

  acl = "public-read"

  lifecycle {
    ignore_changes = [bucket, id]
  }
}

resource "aws_s3_bucket_public_access_block" "s3_admin_thealpinestudio_public_block" {
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false

  lifecycle {
    ignore_changes = [bucket, id]
  }
}

resource "aws_s3_bucket_ownership_controls" "s3_admin_thealpinestudio_ownership" {
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }

  lifecycle {
    ignore_changes = [bucket, id]
  }
}
resource "aws_s3_bucket_policy" "s3_admin_thealpinestudio_policy" {
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicRead"
        Effect    = "Allow"
        Principal = "*"
        "Action" : [
          "s3:GetObject",
        ],
        Resource = "${aws_s3_bucket.s3_admin_thealpinestudio_bucket.arn}/*"
      },
    ]
  })
  lifecycle {
    ignore_changes = [bucket, id, policy]
  }
}

#BUCKET CORS
resource "aws_s3_bucket_cors_configuration" "s3_cors" {
  bucket = aws_s3_bucket.s3_admin_thealpinestudio_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE"]
    allowed_origins = ["https://${aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.domain_name}"]

    expose_headers  = ["ETag"]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_methods = ["POST", "GET", "PUT", "DELETE"]
    allowed_origins = ["https://${aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.domain_name}"]

  }
}

//S3 BUCKET FOR PRODUCT IMAGES
resource "aws_s3_bucket" "s3_product_images_bucket" {
  bucket = var.image_bucket_name


  tags = {
    Name = "Product Images Bucket"
  }
}

resource "aws_s3_bucket_public_access_block" "product_images_public_access_block" {
  bucket = aws_s3_bucket.s3_product_images_bucket.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_policy" "product_images_bucket_policy" {
  bucket = aws_s3_bucket.s3_product_images_bucket.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject",
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.s3_product_images_bucket.arn}/*"
      }
    ]
  })
}



resource "aws_cloudfront_origin_access_control" "product_images_oac" {
  name                              = "product-images-oac"
  description                       = "OAC for product images S3 bucket"
  signing_behavior                  = "always"
  signing_protocol                  = "sigv4"
  origin_access_control_origin_type = "s3"
}


resource "aws_cloudfront_distribution" "product_images_distribution" {
  origin {
    domain_name = aws_s3_bucket.s3_product_images_bucket.bucket_regional_domain_name
    origin_id   = "ProductImagesS3Origin"
    s3_origin_config {
      origin_access_identity = aws_cloudfront_origin_access_identity.product_images_oai.cloudfront_access_identity_path
    }
  }

  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "index.html"

  default_cache_behavior {
    allowed_methods        = ["GET", "HEAD"]
    cached_methods         = ["GET", "HEAD"]
    target_origin_id       = "ProductImagesS3Origin"
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 0
    default_ttl            = 3600
    max_ttl                = 86400
    compress               = true
  }

  viewer_certificate {
    cloudfront_default_certificate = true
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  tags = {
    Name = "Product Images Distribution"
  }
}

resource "aws_cloudfront_origin_access_identity" "product_images_oai" {

  comment = "OAI for Product Images S3 Bucket"
}




#ROUTE53
# resource "aws_route53_record" "admin_domain" {
#   zone_id = var.zone_id
#   name    = "admin.thealpinestudio.com"
#   type    = "A"

#   alias {
#     name                   = aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.domain_name
#     zone_id                = aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.hosted_zone_id
#     evaluate_target_health = false
#   }
# }

# resource "aws_route53_record" "admin_sub_domain" {
#   zone_id = var.zone_id
#   name    = "www.admin.thealpinestudio.com"
#   type    = "A"

#   alias {
#     name                   = aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.domain_name
#     zone_id                = aws_cloudfront_distribution.admin_thealpinestudio_s3_distribution.hosted_zone_id
#     evaluate_target_health = false
#   }
# }

//ACM CERT FOR S3 WEB HOSTING
resource "aws_acm_certificate" "admin_ssl_certificate" {
  provider    = aws.us_east_1
  domain_name = "admin.thealpinestudio.com"
  subject_alternative_names = [
    "www.admin.thealpinestudio.com",
    "site.admin.thealpinestudio.com"
  ]
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }
}


resource "aws_cloudfront_distribution" "admin_thealpinestudio_s3_distribution" {
  origin {
    domain_name = aws_s3_bucket.s3_admin_thealpinestudio_bucket.bucket_regional_domain_name
    origin_id   = local.s3_origin_id
    custom_origin_config {
      http_port              = 80
      https_port             = 443
      origin_protocol_policy = "http-only"
      origin_ssl_protocols   = ["TLSv1", "TLSv1.1", "TLSv1.2"]
    }
  }


  enabled             = true
  is_ipv6_enabled     = true
  default_root_object = "admin/login/admin.html"

  aliases = ["admin.thealpinestudio.com", "www.admin.thealpinestudio.com", "site.admin.thealpinestudio.com"]

  custom_error_response {
    error_caching_min_ttl = 0
    error_code            = 404
    response_code         = 200
    response_page_path    = "/admin.html"
  }

  default_cache_behavior {
    allowed_methods  = ["DELETE", "GET", "HEAD", "OPTIONS", "PATCH", "POST", "PUT"]
    cached_methods   = ["GET", "HEAD"]
    target_origin_id = local.s3_origin_id
    forwarded_values {
      query_string = false
      cookies {
        forward = "none"
      }
    }
    viewer_protocol_policy = "redirect-to-https"
    min_ttl                = 31536000
    default_ttl            = 31536000
    max_ttl                = 31536000
    compress               = true
  }

  restrictions {
    geo_restriction {
      restriction_type = "none"
    }
  }

  viewer_certificate {
    cloudfront_default_certificate = false
    acm_certificate_arn            = aws_acm_certificate.admin_ssl_certificate.arn
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
}
