//./admin/modules/dynamodb/main.tf

resource "aws_dynamodb_table" "products" {
  name           = var.table_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = var.hash_key
  range_key      = var.range_key
  stream_enabled = false

  attribute {
    name = "ID"
    type = "S"
  }

  attribute {
    name = "Title"
    type = "S"
  }

  attribute {
    name = "category"
    type = "S"
  }

  global_secondary_index {
    name            = "CategoryIndex"
    hash_key        = "category"
    projection_type = "ALL"
  }

  tags = {
    Name = "products"
  }

  lifecycle {
    ignore_changes = [replica]
  }
}

//create a DynamoDB table for admin users
//should have email and phone number as the primary key
resource "aws_dynamodb_table" "users" {
  name           = var.dynamo_table_users_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "email"
  range_key      = "phone_number"
  stream_enabled = false

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "phone_number"
    type = "S"
  }

  tags = {
    Name = "users"
  }

  lifecycle {
    ignore_changes = [replica]
  }
}

//create a DynamoDB table for TOP SECRETS
resource "aws_dynamodb_table" "totp_secrets_table" {
  name         = var.dynamo_table_totp_name
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "email"

  attribute {
    name = "email"
    type = "S"
  }

  tags = {
    Name = "admin-totp_secrets"
  }

  lifecycle {
    ignore_changes = [replica]
  }
}

resource "aws_dynamodb_table" "orders" {
  name           = var.dynamo_table_orders_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "order_id"
  range_key      = "user_email" // Optional: if you want to query orders by user
  stream_enabled = false

  attribute {
    name = "order_id"
    type = "S"
  }

  attribute {
    name = "user_email"
    type = "S"
  }

  global_secondary_index {
    name            = "UserEmailIndex"
    hash_key        = "user_email"
    projection_type = "ALL"
  }

  tags = {
    Name = "orders"
  }

  lifecycle {
    ignore_changes = [replica]
  }
}


resource "aws_iam_policy" "dynamodb_policy" {
  name        = "dynamodb_policy"
  description = "Policy for accessing DynamoDB tables"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:GetItem",
          "dynamodb:DescribeTable",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ],
        Resource = [
          aws_dynamodb_table.products.arn,
          "${aws_dynamodb_table.products.arn}/index/CategoryIndex",
          "${aws_dynamodb_table.products.arn}/index/SubcategoryIndex", # Add this line if using Option A
          "${aws_dynamodb_table.products.arn}/index/CategorySubcategoryIndex",
          aws_dynamodb_table.orders.arn,

          aws_dynamodb_table.users.arn,
          aws_dynamodb_table.totp_secrets_table.arn,
          "${aws_dynamodb_table.orders.arn}/index/UserEmailIndex",
          "${aws_dynamodb_table.orders.arn}/index/admin-orders"

        ]
      },
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:AdminGetUser",
          "cognito-idp:ListUsers"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role" "lambda_role" {
  name               = "lambda_role"
  assume_role_policy = <<EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "dynamodb_policy_attachment" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.dynamodb_policy.arn
}
