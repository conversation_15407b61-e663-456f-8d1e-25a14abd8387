//./admin/modules/admin-lambda/get-products.tf
//get PRODUCTS FUNCTION
//----------------------------------------------------------------

data "archive_file" "lambda_code_get" {
  type        = "zip"
  source_dir  = "../../../lambda/adminListProducts/"
  output_path = "./admin-list-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "lambda_code_get" {
  bucket       = var.s3_bucket
  key          = "admin-list-products.zip"
  source       = data.archive_file.lambda_code_get.output_path
  etag         = filemd5(data.archive_file.lambda_code_get.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "get_products_function" {
  function_name = var.get_lambda_function_name
  s3_bucket     = aws_s3_object.lambda_code_get.bucket
  s3_key        = aws_s3_object.lambda_code_get.key
  role          = aws_iam_role.lambda_execution_role_get.arn
  handler       = "admin-list-products"
  runtime       = "provided.al2023"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_get.output_base64sha256

  environment {
    variables = {

      "TABLE_NAME"        = var.dynamo_table_products_name
      "IMAGE_BUCKET_NAME" = var.image_bucket_name
    }
  }
}
resource "aws_lambda_permission" "allow_api_gateway_invoke" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.get_products_function.arn
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${var.admin_api_gateway_arn}/*/GET/products"
}


resource "aws_lambda_permission" "lambda_api_gw_permissions_get" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.get_lambda_function_name}1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.get_products_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${var.admin_api_gateway_id}/*/GET/products"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

# resource "null_resource" "remove_lambda_permission_get" {
#   triggers = {
#     statement_id = aws_lambda_permission.lambda_api_gw_permissions_get.statement_id
#   }
#   provisioner "local-exec" {
#   environment = {
#     AWS_DEFAULT_REGION = "us-west-2"
#   }
#   command = "aws lambda remove-permission --function-name arn:aws:lambda:us-west-2:************:function:get-products --statement-id AllowExecutionFromAPIGatewayV1"
# }

# depends_on = [
#   aws_lambda_permission.lambda_api_gw_permissions_get,
# ]

# # Only run the provisioner when the statement ID changes
# # and ignore other changes in the null_resource
# lifecycle {
#   create_before_destroy = true
#   ignore_changes        = all
# }
# }

resource "aws_iam_role" "lambda_execution_role_get" {
  name = "lambda_execution_role_get_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_get" {
  name = "lambda_execution_policy_list_products_function"
  role = aws_iam_role.lambda_execution_role_get.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}",
          "arn:aws:s3:::${var.image_bucket_name}",
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}/index/*"

        ]

      }
    ]
  })
}

