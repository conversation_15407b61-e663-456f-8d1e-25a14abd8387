//admin/modules/admin-lambda/variables.tf
variable "s3_bucket" {
  type        = string
  description = "The name of the S3 bucket to store the lambda functions"
}

variable "aws_region" {
  description = "The AWS region"
  type        = string

}

variable "sns_topic_name" {
  description = "The name of the SNS topic"
  type        = string
  
}
variable "aws_account_id" {
  description = "The AWS account ID"
  type        = string

}

variable "admin_api_gateway_arn" {
  description = "ARN of the Admin API Gateway"
  type        = string

}

variable "api_gateway_account_id" {
  description = "The ID of the AWS account that owns the API Gateway"
  type        = string
}

variable "admin_api_gateway_id" {
  description = "ID of the Admin API Gateway"
  type        = string
}


variable "admin_user_pool_id" {
  description = "The ID of the Cognito User Pool"
  type        = string
  
}

variable dynamo_table_users_name {
  description = "The name of the DynamoDB table"
  type        = string
}

variable "image_bucket_name" {
  description = "The name of the S3 bucket to store images"
  type        = string
}

variable "presign_lambda_function_name" {
  type        = string
  description = "Name of the presigned URL Lambda function"
}

variable "resizer_lambda_function_name" {
  description = "The name of the resizer Lambda function"
  type        = string
}

variable "get_lambda_function_name" {
  type        = string
  description = "The name of the GET Lambda function"
}

variable "put_lambda_function_name" {
  type        = string
  description = "The name of the PUT Lambda function"
}



variable "get_product_by_id_lambda_function_name" {
  description = "The name of the Lambda function that retrieves a product by ID"
  type        = string
  
}

variable "post_lambda_function_name" {
  type        = string
  description = "The name of the POST Lambda function"
}

variable "auth_cognito_refresh_token_function_name" {
  type = string
  description = "lambda function name for cognito refresh token"
}


variable "delete_lambda_function_name" {
  type        = string
  description = "The name of the DELETE Lambda function"
}

variable "dynamo_table_products_name" {
  description = "The name of the DynamoDB table"
  type        = string
  
}

variable "client_id" {
  description = "The client ID for the Cognito User Pool"
  type        = string

}

variable "client_secret" {
  description = "The client secret for the Cognito User Pool"
  type        = string
}

variable "ses_sender" {
  description = "The email address of the sender"
  type        = string
}

//verify mfa function
variable "auth_cognito_verify_mfa_function_name" {
  description = "lambda function name for cognito verify mfa"
  type = string
}

//ttop name
variable "auth_cognito_top_function_name" {
  description = "lambda function name for cognito logout"
  type = string
}

//dynamodb ttop table name
variable "dynamo_table_totp_name" {
  description = "The TOTP secrets"
  type        = string
  
}


//login name
variable "auth_cognito_login_function_name" {
  description = "lambda function name for cognito login"
  type = string
}
//logout name
variable "auth_cognito_logout_function_name" {
  description = "lambda function name for cognito logout"
  type = string
}

//forgot password name
variable "auth_cognito_forgot_password_function_name" {
  description = "lambda function name for cognito forgot password"
  type = string
}



//resend code name
variable "auth_cognito_resend_code_function_name" {
  description = "lambda function name for cognito resend code"
  type = string
}

//pre sign up name
variable "auth_cognito_pre_signup_function_name" {
  description = "lambda function name for cognito pre sign up"
  type = string
}

variable "auth_cognito_signup_function_name" {
  description = "lambda function name for cognito sign up"
  type = string
  
}

//Confirm sign up name
variable "auth_cognito_confirm_signup_function_name" {
  description = "lambda function name for cognito confirm sign up"
  type = string
}

//define  auth challenge name
variable "auth_cognito_define_auth_challenge_function_name" {
  description = "lambda function name for cognito define auth challenge"
  type = string
}

//post authentication name
variable "auth_cognito_post_authentication_function_name" {
  description = "lambda function name for cognito post authentication"
  type = string
}

//post confirmation name
variable "auth_cognito_post_confirmation_function_name" {
  description = "lambda function name for cognito post confirmation"
  type = string
}

//pre authentication name
variable "auth_cognito_pre_authentication_function_name" {
  description = "lambda function name for cognito pre authentication"
  type = string
}

//post verify auth challenge response name
variable "auth_cognito_verify_auth_challenge_response_function_name" {
  description = "lambda function name for cognito verify auth challenge response"
  type = string
} 

variable "auth_cognito_create_auth_challenge_function_name" {
  description = "lambda function name for cognito create auth challenge"
  type = string

}


//sold product name
variable "auth_cognito_sold_product_function_name" {
  description = "lambda function name for cognito sold product"
  type = string
}

variable "token_authorizer_lambda_function_name" {
  description = "The name of the token authorizer Lambda function"
  type        = string
}

variable "jwt_secret" {
  description = "The secret for the JWT"
  type        = string
  
}