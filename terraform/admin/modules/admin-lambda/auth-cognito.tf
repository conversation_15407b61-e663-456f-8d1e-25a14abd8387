data "aws_caller_identity" "current" {}



//auth_cognito PRODUCTS FUNCTION
//----------------------------------------------------------------

data "archive_file" "auth_login_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authLogin/"
  output_path = "./auth-login.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "auth_login_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-login.zip"
  source       = data.archive_file.auth_login_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_login_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

//Login Function
//----------------------------------------------------------------
resource "aws_lambda_function" "auth_login_lambda_functions" {
  function_name    = var.auth_cognito_login_function_name
  s3_bucket        = aws_s3_object.auth_login_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_login_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-login.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_login_lambda_archive.output_base64sha256
  timeout          = 10


  environment {
    variables = {
      "BUCKET_NAME"    = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      "CLIENT_ID"      = var.client_id
      "CLIENT_SECRET"  = var.client_secret
      "USER_POOL_ID"   = var.admin_user_pool_id
      "DYNAMO_TABLE"   = var.dynamo_table_users_name
    }
  }
}



resource "aws_lambda_permission" "auth_login_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_login_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_login_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/auth-login"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "auth_pre_signup_lambda_cognito_permission" {
  statement_id  = "AllowCognitoInvoke-${var.auth_cognito_pre_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_pre_signup_lambda_functions.arn

  principal  = "cognito-idp.amazonaws.com"
  source_arn = "arn:aws:cognito-idp:${var.aws_region}:${var.aws_account_id}:userpool/${var.admin_user_pool_id}"
}





//LOGOUT FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_logout_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authLogout/"
  output_path = "./auth-logout.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_logout_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-logout.zip"
  source       = data.archive_file.auth_logout_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_logout_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_logout_lambda_functions" {
  function_name    = var.auth_cognito_logout_function_name
  s3_bucket        = aws_s3_object.auth_logout_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_logout_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-logout.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_logout_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
    }
  }
}

resource "aws_lambda_permission" "auth_logout_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_logout_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_logout_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/logout"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//REFRESH TOKEN FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_refresh_token_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authRefreshToken/"
  output_path = "./auth-refresh-token.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_refresh_token_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-refresh-token.zip"
  source       = data.archive_file.auth_refresh_token_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_refresh_token_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_refresh_token_lambda_functions" {
  function_name    = var.auth_cognito_refresh_token_function_name
  s3_bucket        = aws_s3_object.auth_refresh_token_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_refresh_token_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-refresh-token.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_refresh_token_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
    }
  }
}

resource "aws_lambda_permission" "auth_refresh_token_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_refresh_token_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_refresh_token_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/refresh-token"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//FORGOT PASSWORD FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_forgot_password_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authForgotPassword/"
  output_path = "./auth-forgot-password.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_forgot_password_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-forgot-password.zip"
  source       = data.archive_file.auth_forgot_password_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_forgot_password_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_forgot_password_lambda_functions" {
  function_name    = var.auth_cognito_forgot_password_function_name
  s3_bucket        = aws_s3_object.auth_forgot_password_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_forgot_password_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-forgot-password.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_forgot_password_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"    = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      "CLIENT_ID"      = var.client_id
      "CLIENT_SECRET"  = var.client_secret
    }
  }
}

resource "aws_lambda_permission" "auth_forgot_password_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_forgot_password_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_forgot_password_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/forgot-password"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}




//RESEND CODE FUNCTION
//----------------------------------------------------------------

data "archive_file" "auth_resend_code_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authResendCode/"
  output_path = "./auth-resend-code.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_resend_code_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-resend-code.zip"
  source       = data.archive_file.auth_resend_code_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_resend_code_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_resend_code_lambda_functions" {
  function_name    = var.auth_cognito_resend_code_function_name
  s3_bucket        = aws_s3_object.auth_resend_code_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_resend_code_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-resend-code.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_resend_code_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"    = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      "CLIENT_ID"      = var.client_id
      "CLIENT_SECRET"  = var.client_secret
    }
  }
}

resource "aws_lambda_permission" "auth_resend_code_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_resend_code_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_resend_code_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/resend-code"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}



//PRE SIGNUP FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_pre_signup_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authPreSignup/"
  output_path = "./auth-pre-signup.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_pre_signup_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-pre-signup.zip"
  source       = data.archive_file.auth_pre_signup_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_pre_signup_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_pre_signup_lambda_functions" {
  function_name    = var.auth_cognito_pre_signup_function_name
  s3_bucket        = aws_s3_object.auth_pre_signup_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_pre_signup_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-pre-signup.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_pre_signup_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
      "DYNAMO_TABLE"  = var.dynamo_table_users_name
      "USER_POOL_ID"  = var.admin_user_pool_id
    }
  }
}

resource "aws_lambda_permission" "auth_pre_signup_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_pre_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_pre_signup_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/pre-signup"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//SIGNUP FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_signup_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authSignup/"
  output_path = "./auth-signup.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_signup_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-signup.zip"
  source       = data.archive_file.auth_signup_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_signup_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_signup_lambda_functions" {
  function_name    = var.auth_cognito_signup_function_name
  s3_bucket        = aws_s3_object.auth_signup_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_signup_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-signup.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_signup_lambda_archive.output_base64sha256
  timeout          = 10


  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
      "DYNAMO_TABLE"  = var.dynamo_table_users_name
      "USER_POOL_ID"  = var.admin_user_pool_id
    }
  }
}

resource "aws_lambda_permission" "auth_signup_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_signup_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/signup"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//CONFIRM SIGNUP FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_confirm_signup_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authConfirmSignup/"
  output_path = "./auth-confirm-signup.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_confirm_signup_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-confirm-signup.zip"
  source       = data.archive_file.auth_confirm_signup_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_confirm_signup_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_confirm_signup_lambda_functions" {
  function_name    = var.auth_cognito_confirm_signup_function_name
  s3_bucket        = aws_s3_object.auth_confirm_signup_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_confirm_signup_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-confirm-signup.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_confirm_signup_lambda_archive.output_base64sha256
  timeout          = 10


  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
      "ADMIN_USERS"   = var.dynamo_table_users_name
      "USER_POOL_ID"  = var.admin_user_pool_id
    }
  }
}

resource "aws_lambda_permission" "auth_confirm_signup_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_confirm_signup_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_confirm_signup_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/confirm-signup"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}


//VERIFY MFA FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_verify_mfa_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authVerifyMFA/"
  output_path = "./auth-verify-mfa.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_verify_mfa_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-verify-mfa.zip"
  source       = data.archive_file.auth_verify_mfa_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_verify_mfa_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_verify_mfa_lambda_functions" {
  function_name    = var.auth_cognito_verify_mfa_function_name
  s3_bucket        = aws_s3_object.auth_verify_mfa_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_verify_mfa_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-verify-mfa.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_verify_mfa_lambda_archive.output_base64sha256
  timeout          = 10


  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
      "USER_POOL_ID"  = var.admin_user_pool_id
    }
  }
}

resource "aws_lambda_permission" "auth_verify_mfa_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_verify_mfa_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_verify_mfa_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/verify-mfa"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//TTOP Lambda Functions
//----------------------------------------------------------------
data "archive_file" "auth_top_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authTtopSecret/"
  output_path = "./auth-totp-setup.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_top_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-totp-setup.zip"
  source       = data.archive_file.auth_top_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_top_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_top_lambda_functions" {
  function_name    = var.auth_cognito_top_function_name
  s3_bucket        = aws_s3_object.auth_top_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_top_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-totp-setup.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_top_lambda_archive.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      "BUCKET_NAME"     = var.s3_bucket
      "CLIENT_ID"       = var.client_id
      "CLIENT_SECRET"   = var.client_secret
      "USER_POOL_ID"    = var.admin_user_pool_id
      "TOTP_TABLE_NAME" = var.dynamo_table_totp_name
    }
  }
}

resource "aws_lambda_permission" "auth_top_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_top_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_top_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/totp-secret"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

# //CUSTOM MESSAGE FUNCTION
# //----------------------------------------------------------------
# data "archive_file" "auth_custom_message_lambda_archive" {
#   type        = "zip"
#   source_dir  = "../../../lambda/authCustomMessage/"
#   output_path = "./auth-custom-message.zip"

#   # depends_on  = [null_resource.install_dependencies]
#   # Exclude the venv/bin/python file
#   # excludes = ["lambda_venv/**"]
# }

# resource "aws_s3_object" "auth_custom_message_lambda_s3_object" {
#   bucket       = var.s3_bucket
#   key          = "auth-custom-message.zip"
#   source       = data.archive_file.auth_custom_message_lambda_archive.output_path
#   etag         = filemd5(data.archive_file.auth_custom_message_lambda_archive.output_path)
#   acl          = "private"
#   content_type = "application/zip"
# }

# resource "aws_lambda_function" "auth_custom_message_lambda_functions" {
#   function_name    = var.auth_cognito_custom_message_function_name
#   s3_bucket        = aws_s3_object.auth_custom_message_lambda_s3_object.bucket
#   s3_key           = aws_s3_object.auth_custom_message_lambda_s3_object.key
#   role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
#   handler          = "auth-custom-message.lambda_handler"
#   runtime          = "provided.al2"
#   source_code_hash = data.archive_file.auth_custom_message_lambda_archive.output_base64sha256

#   environment {
#     variables = {
#       "BUCKET_NAME"    = var.s3_bucket
#       "CLIENT_ID"      = var.client_id
#       "CLIENT_SECRET"  = var.client_secret
#     }
#   }
# }

# resource "aws_lambda_permission" "auth_custom_message_lambda_permissions" {
#   statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_custom_message_function_name}"
#   action        = "lambda:InvokeFunction"
#   function_name = aws_lambda_function.auth_custom_message_lambda_functions.arn

#   principal  = "apigateway.amazonaws.com"
#   source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_arn}/*/POST/custom-message"

#   lifecycle {
#     ignore_changes = [
#       statement_id,
#       source_arn,
#       function_name,
#     ]
#   }
# }

//DEFINE POST AUTH CHALLENGE FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_define_auth_challenge_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authDefineAuthChallenge/"
  output_path = "./auth-define-auth-challenge.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_define_auth_challenge_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-define-auth-challenge.zip"
  source       = data.archive_file.auth_define_auth_challenge_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_define_auth_challenge_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_define_auth_challenge_lambda_functions" {
  function_name    = var.auth_cognito_define_auth_challenge_function_name
  s3_bucket        = aws_s3_object.auth_define_auth_challenge_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_define_auth_challenge_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-define-auth-challenge.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_define_auth_challenge_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"   = var.s3_bucket
      "CLIENT_ID"     = var.client_id
      "CLIENT_SECRET" = var.client_secret
    }
  }
}

resource "aws_lambda_permission" "auth_define_auth_challenge_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_define_auth_challenge_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_define_auth_challenge_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/define-auth-challenge"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//POST AUTH CHALLENGE FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_post_auth_challenge_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authPostAuthChallenge/"
  output_path = "./auth-post-auth-challenge.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_post_auth_challenge_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-post-auth-challenge.zip"
  source       = data.archive_file.auth_post_auth_challenge_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_post_auth_challenge_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_post_auth_challenge_lambda_functions" {
  function_name    = var.auth_cognito_post_authentication_function_name
  s3_bucket        = aws_s3_object.auth_post_auth_challenge_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_post_auth_challenge_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-post-auth-challenge.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_post_auth_challenge_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"     = var.s3_bucket
      "CLIENT_ID"       = var.client_id
      "CLIENT_SECRET"   = var.client_secret
      "USER_POOL_ID"    = var.admin_user_pool_id
      "TOTP_TABLE_NAME" = var.dynamo_table_totp_name
      "SENDER_EMAIL"    = "<EMAIL>"
    }
  }
}

resource "aws_lambda_permission" "auth_post_auth_challenge_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_post_authentication_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_post_auth_challenge_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/post-auth-challenge"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}




//POST AUTH CONFIRMATION FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_post_confirmation_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authPostConfirmation/"
  output_path = "./auth-post-confirmation.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_post_confirmation_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-post-confirmation.zip"
  source       = data.archive_file.auth_post_confirmation_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_post_confirmation_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_post_confirmation_lambda_functions" {
  function_name    = var.auth_cognito_post_confirmation_function_name
  s3_bucket        = aws_s3_object.auth_post_confirmation_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_post_confirmation_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-post-confirmation.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_post_confirmation_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"     = var.s3_bucket
      "CLIENT_ID"       = var.client_id
      "CLIENT_SECRET"   = var.client_secret
      "TOTP_TABLE_NAME" = var.dynamo_table_totp_name
    }
  }
}

resource "aws_lambda_permission" "auth_post_confirmation_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_post_confirmation_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_post_confirmation_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/postconfirmation"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}



//PRE AUTHENTICATION FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_pre_authentication_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authPreAuthentication/"
  output_path = "./auth-pre-authentication.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_pre_authentication_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-post-auth-confirmation.zip"
  source       = data.archive_file.auth_pre_authentication_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_pre_authentication_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_pre_authentication_lambda_functions" {
  function_name    = var.auth_cognito_pre_authentication_function_name
  s3_bucket        = aws_s3_object.auth_pre_authentication_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_pre_authentication_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-pre-authentication.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_pre_authentication_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"     = var.s3_bucket
      "CLIENT_ID"       = var.client_id
      "CLIENT_SECRET"   = var.client_secret
      "TOTP_TABLE_NAME" = var.dynamo_table_totp_name
    }
  }
}

resource "aws_lambda_permission" "auth_pre_authentication_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_pre_authentication_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_pre_authentication_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/pre-authentication"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//VERIFY AUTH CHALLENGE RESPONSE FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_verify_auth_challenge_response_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authVerifyAuthChallengeResponse/"
  output_path = "./auth-verify-auth-challenge-response.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_verify_auth_challenge_response_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-verify-auth-challenge-response.zip"
  source       = data.archive_file.auth_verify_auth_challenge_response_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_verify_auth_challenge_response_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_verify_auth_challenge_response_lambda_functions" {
  function_name    = var.auth_cognito_verify_auth_challenge_response_function_name
  s3_bucket        = aws_s3_object.auth_verify_auth_challenge_response_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_verify_auth_challenge_response_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-verify-auth-challenge-response.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_verify_auth_challenge_response_lambda_archive.output_base64sha256

  environment {
    variables = {
      DYNAMO_TABLE = var.dynamo_table_users_name
    }
  }
}

resource "aws_lambda_permission" "auth_verify_auth_challenge_response_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_verify_auth_challenge_response_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_verify_auth_challenge_response_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/verify-auth-challenge-response"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

//CREATE AUTH CHALLENGE FUNCTION
//----------------------------------------------------------------
data "archive_file" "auth_create_auth_challenge_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/authCreateAuthChallenge/"
  output_path = "./auth-create-auth-challenge.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "auth_create_auth_challenge_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "auth-create-auth-challenge.zip"
  source       = data.archive_file.auth_create_auth_challenge_lambda_archive.output_path
  etag         = filemd5(data.archive_file.auth_create_auth_challenge_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "auth_create_auth_challenge_lambda_functions" {
  function_name    = var.auth_cognito_create_auth_challenge_function_name
  s3_bucket        = aws_s3_object.auth_create_auth_challenge_lambda_s3_object.bucket
  s3_key           = aws_s3_object.auth_create_auth_challenge_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "auth-create-auth-challenge.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.auth_create_auth_challenge_lambda_archive.output_base64sha256

  environment {
    variables = {
      DYNAMO_TABLE = var.dynamo_table_users_name
      SES_SENDER   = var.ses_sender
    }
  }
}

resource "aws_lambda_permission" "auth_create_auth_challenge_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_create_auth_challenge_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.auth_create_auth_challenge_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/create-auth-challenge"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}




//IAM
resource "aws_iam_role" "lambda_execution_role_auth_cognito" {
  name = "lambda_execution_role_auth_cognito_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_auth_cognito" {
  name = "lambda_execution_policy_auth_cognito_function"
  role = aws_iam_role.lambda_execution_role_auth_cognito.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:${var.aws_region}:${var.aws_account_id}:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:GetObject",
          "s3:PutObjectAcl"
        ]
        Resource = "arn:aws:s3:::${var.s3_bucket}/*"
      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:${var.aws_region}:${var.aws_account_id}:*"
      },
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:SignUp",
          "cognito-idp:AdminEnableUser",
          "cognito-idp:AdminCreateUser",
          "cognito-idp:AdminSetUserPassword",
          "cognito-idp:AdminConfirmSignUp",
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminUpdateUserAttributes",
          "cognito-idp:ListUsers",
          "cognito-idp:AdminRespondToAuthChallenge",
          "cognito-idp:AdminInitiateAuth",
          "cognito-idp:AdminSetUserMFAPreference",

        ],
        Resource = "arn:aws:cognito-idp:${var.aws_region}:${var.aws_account_id}:userpool/${var.admin_user_pool_id}"
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:GetItem",
          "dynanodb:UpdateItem",
          "dynamodb:DeleteItem"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_users_name}",
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}",
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/mfa_codes",
          "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_totp_name}"

        ]
      },
      {
        Effect = "Allow",
        Action = [
          "sns:Publish"
        ],
        Resource = "*"
      }

    ]
  })
}

