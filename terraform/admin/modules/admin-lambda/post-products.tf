//post PRODUCTS FUNCTION
//----------------------------------------------------------------


data "archive_file" "lambda_code_post" {
  type        = "zip"
  source_dir  = "../../../lambda/adminListProducts/"
  output_path = "./post-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "lambda_code_post" {
  bucket       = var.s3_bucket
  key          = "post-products.zip"
  source       = data.archive_file.lambda_code_post.output_path
  etag         = filemd5(data.archive_file.lambda_code_post.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "post_products_function" {
  function_name = var.post_lambda_function_name
  s3_bucket     = aws_s3_object.lambda_code_post.bucket
  s3_key        = aws_s3_object.lambda_code_post.key
  role          = aws_iam_role.lambda_execution_role_post.arn
  handler       = "post-products"
  runtime       = "provided.al2023"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_post.output_base64sha256

  environment {
    variables = {
      "TABLE_NAME"        = var.dynamo_table_products_name
      "IMAGE_BUCKET_NAME" = var.image_bucket_name

    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_post" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.post_lambda_function_name}1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.post_products_function.function_name

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:${var.admin_api_gateway_id}/*/POST/products"


}

# resource "null_resource" "remove_lambda_permission_post" {
#   triggers = {
#     statement_id = aws_lambda_permission.lambda_api_gw_permissions_post.statement_id
#   }
#   provisioner "local-exec" {
#   environment = {
#     AWS_DEFAULT_REGION = "us-west-2"
#   }
#   command = "aws lambda remove-permission --function-name arn:aws:lambda:us-west-2:************:function:post-products --statement-id AllowExecutionFromAPIGatewayV1"
# }

# depends_on = [
#   aws_lambda_permission.lambda_api_gw_permissions_post,
# ]

# # Only run the provisioner when the statement ID changes
# # and ignore other changes in the null_resource
# lifecycle {
#   create_before_destroy = true
#   ignore_changes        = all
# }
# }

resource "aws_iam_role" "lambda_execution_role_post" {
  name = "lambda_execution_role_post_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_post" {
  name = "lambda_execution_policy_post_products_function"
  role = aws_iam_role.lambda_execution_role_post.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:*"
      },
      {
        Effect = "Allow",
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl"
        ],
        Resource = [
          "arn:aws:s3:::${var.image_bucket_name}/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "execute-api:Invoke"
        ],
        Resource = "arn:aws:execute-api:${var.aws_region}:${data.aws_caller_identity.current.account_id}:*"
      },
      {
        Effect = "Allow",
        Action = [
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:GetItem",
          "dynamodb:Scan",
          "dynamodb:Query"
        ],
        Resource = ["arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}",
        "arn:aws:s3:::${var.image_bucket_name}/*"]
      }
    ]
  })
}
