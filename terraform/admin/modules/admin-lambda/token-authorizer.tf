data "archive_file" "lambda_code_token_authorizer" {
  type        = "zip"
  source_dir  = "../../../lambda/tokenAuthorizer/"
  output_path = "./token-authorizer.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "lambda_code_token_authorizer" {
  bucket       = var.s3_bucket
  key          = "token-authorizer.zip"
  source       = data.archive_file.lambda_code_token_authorizer.output_path
  etag         = filemd5(data.archive_file.lambda_code_token_authorizer.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "token_authorizer_function" {
  function_name = var.token_authorizer_lambda_function_name
  s3_bucket     = aws_s3_object.lambda_code_token_authorizer.bucket
  s3_key        = aws_s3_object.lambda_code_token_authorizer.key
  role          = aws_iam_role.lambda_execution_role_token_authorizer.arn
  handler       = "token-authorizer"
  runtime       = "provided.al2023"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_token_authorizer.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      "JWTSECRET"   = var.jwt_secret
      "USER_POOL_ID" = var.admin_user_pool_id
      "CLIENT_SECRET" = var.client_secret
      "CLIENT_ID" = var.client_id

    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_token_authorizer" {
  statement_id  = "AllowInvoke-${var.token_authorizer_lambda_function_name}1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.token_authorizer_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/*"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}



resource "aws_iam_role" "lambda_execution_role_token_authorizer" {
  name = "lambda_execution_role_token_authorizer"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = ["lambda.amazonaws.com", "apigateway.amazonaws.com"]
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}


resource "aws_iam_policy" "lambda_execution_policy" {
  name = "LambdaExecutionPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect : "Allow",
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource : "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:*"
      },
      {
        Effect : "Allow",
        Action : [
          "lambda:InvokeFunction"
        ],
        Resource : "arn:aws:lambda:${var.aws_region}:${data.aws_caller_identity.current.account_id}:function:${var.token_authorizer_lambda_function_name}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_attach_policy" {
  role       = aws_iam_role.lambda_execution_role_token_authorizer.name
  policy_arn = aws_iam_policy.lambda_execution_policy.arn
}

