//delete PRODUCTS FUNCTION
//----------------------------------------------------------------

data "archive_file" "lambda_code_delete" {
  type        = "zip"
  source_dir  = "../../../lambda/adminDeleteProduct/"
  output_path = "./delete-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "lambda_code_delete" {
  bucket       = var.s3_bucket
  key          = "delete-products.zip"
  source       = data.archive_file.lambda_code_delete.output_path
  etag         = filemd5(data.archive_file.lambda_code_delete.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "delete_products_function" {
  function_name = var.delete_lambda_function_name
  s3_bucket     = aws_s3_object.lambda_code_delete.bucket
  s3_key        = aws_s3_object.lambda_code_delete.key
  role          = aws_iam_role.lambda_execution_role_delete.arn
  handler       = "delete-products"
  runtime       = "provided.al2023"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_delete.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      "TABLE_NAME" = var.dynamo_table_products_name
    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_delete" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.delete_lambda_function_name}1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.delete_products_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_arn}/*/DELETE/products"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

# resource "null_resource" "remove_lambda_permission_delete" {
#   triggers = {
#     statement_id = aws_lambda_permission.lambda_api_gw_permissions_delete.statement_id
#   }
#   provisioner "local-exec" {
#   environment = {
#     AWS_DEFAULT_REGION = "us-west-2"
#   }
#   command = "aws lambda remove-permission --function-name arn:aws:lambda:us-west-2:************:function:delete-products --statement-id AllowExecutionFromAPIGatewayV1"
# }

# depends_on = [
#   aws_lambda_permission.lambda_api_gw_permissions_delete,
# ]

# # Only run the provisioner when the statement ID changes
# # and ignore other changes in the null_resource
# lifecycle {
#   create_before_destroy = true
#   ignore_changes        = all
# }
# }

resource "aws_iam_role" "lambda_execution_role_delete" {
  name = "lambda_execution_role_delete_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_delete" {
  name = "lambda_execution_policy_delete_products_function"
  role = aws_iam_role.lambda_execution_role_delete.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket}/*",
          "arn:aws:s3:::${var.s3_bucket}/*"
          ]

      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:*:*:*"
      },
      {
      Effect = "Allow"
      Action = [
        "dynamodb:PutItem",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:GetItem",
        "dynamodb:Scan",
        "dynamodb:Query",
      ]
        Resource = ["arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/products"]
      }
    ]
  })
}