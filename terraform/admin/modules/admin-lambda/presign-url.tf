
############################
# IAM Role for Lambda
############################

data "archive_file" "lambda_code_presign_url" {
  type        = "zip"
  source_dir  = "../../../lambda/adminPreSignUrl/"
  output_path = "./generate_presign_url.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "lambda_code_presign_url" {
  bucket       = var.s3_bucket
  key          = "generate_presign_url.zip"
  source       = data.archive_file.lambda_code_presign_url.output_path
  etag         = filemd5(data.archive_file.lambda_code_presign_url.output_path)
  acl          = "private"
  content_type = "application/zip"
}
resource "aws_iam_role" "presigned_url_lambda_role" {
  name = "presigned_url_lambda_role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_role_policy" "presigned_url_lambda_policy" {
  name = "presigned_url_lambda_policy"
  role = aws_iam_role.presigned_url_lambda_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      # Logging
      {
        Effect: "Allow",
        Action: [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource: "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:*"
      },
      # S3 PutObject for originals/ prefix
      {
        Effect: "Allow",
        Action: [
          "s3:PutObject"
        ],
        Resource: "arn:aws:s3:::${var.image_bucket_name}/originals/*"
      }
    ]
  })
}

############################
# Lambda Function
############################
resource "aws_lambda_function" "generate_presigned_url" {
  function_name    = var.presign_lambda_function_name
    s3_bucket        = aws_s3_object.lambda_code_presign_url.bucket
    s3_key           = aws_s3_object.lambda_code_presign_url.key
  role             = aws_iam_role.presigned_url_lambda_role.arn
  handler          = "generate_presigned_url" # adjust if needed
  runtime       = "provided.al2023"

  source_code_hash = data.archive_file.lambda_code_presign_url.output_base64sha256

  environment {
    variables = {
      "IMAGE_BUCKET_NAME" = var.image_bucket_name
    }
  }
}

