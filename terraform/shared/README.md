# Shared Infrastructure

This directory contains infrastructure resources that are shared across multiple environments and applications.

## Structure

```
terraform/shared/
├── secrets-manager/    # Stripe API key management
└── README.md          # This file
```

## Secrets Manager

The `secrets-manager/` directory manages Stripe API keys that are used by:
- Production payment processing (test-modules)
- Admin panel functions
- Any other services that need Stripe access

### Benefits of Shared Secrets

1. **Single Source of Truth** - One place to rotate Stripe keys
2. **Consistent Access** - All services use the same secret
3. **Centralized Monitoring** - Unified alerting and logging
4. **Independent Deployment** - Can update secrets without touching application code

### Usage

#### Deploy Shared Secrets
```bash
cd terraform/shared/secrets-manager
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your Stripe keys
terraform init
terraform plan
terraform apply
```

#### Reference from Other Terraform Configurations

**Option 1: Direct Reference (if in same AWS account)**
```hcl
# In your Lambda configuration
environment {
  variables = {
    STRIPE_SECRETS_NAME = "prod/stripe/api-keys"  # From shared secrets output
  }
}

# Attach IAM policy
resource "aws_iam_role_policy_attachment" "stripe_secrets" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::ACCOUNT:policy/prod-stripe-secrets-access"
}
```

**Option 2: Remote State Reference**
```hcl
data "terraform_remote_state" "shared_secrets" {
  backend = "local"  # or "s3" if using remote backend
  config = {
    path = "../shared/secrets-manager/terraform.tfstate"
  }
}

# Use outputs
environment {
  variables = {
    STRIPE_SECRETS_NAME = data.terraform_remote_state.shared_secrets.outputs.secret_name
  }
}
```

### Key Rotation Process

1. **Generate new key in Stripe Dashboard** (requires 2FA)
2. **Update the secret**:
   ```bash
   aws secretsmanager update-secret \
     --secret-id "prod/stripe/api-keys" \
     --secret-string '{"STRIPE_SECRET_KEY":"sk_live_NEW_KEY","STRIPE_ENDPOINT_SECRET":"whsec_...","VERSION":"2.0"}'
   ```
3. **All applications automatically pick up the new key** within 5 minutes

### Monitoring

- CloudWatch logs track secret access
- SNS notifications for unusual access patterns
- Email alerts for rotation reminders

## Adding New Shared Resources

When adding new shared infrastructure:

1. Create a new directory under `terraform/shared/`
2. Follow the same pattern as `secrets-manager/`
3. Include clear documentation and usage examples
4. Tag resources with `Type = "shared-infrastructure"`
