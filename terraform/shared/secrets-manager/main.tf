# Shared Stripe Secrets Manager
# Used by both admin and production infrastructure

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Use the secrets manager module
module "stripe_secrets" {
  source = "../../modules/secrets-manager"

  environment            = var.environment
  stripe_secret_key      = var.stripe_secret_key
  stripe_endpoint_secret = var.stripe_endpoint_secret
  stripe_public_key      = var.stripe_public_key
  rotation_days          = var.rotation_days

  # Cross-region replication removed due to AWS provider limitations

  common_tags = {
    Environment = var.environment
    Project     = "TheAlpineStudio"
    Service     = "stripe-secrets"
    ManagedBy   = "terraform"
    Type        = "shared-infrastructure"
  }

  # SNS topics for alerts
  alarm_sns_topics = var.create_sns_topic ? [aws_sns_topic.stripe_alerts[0].arn] : []
}

# SNS topic for rotation notifications
resource "aws_sns_topic" "stripe_alerts" {
  count = var.create_sns_topic ? 1 : 0
  name  = "${var.environment}-stripe-key-rotation-alerts"

  tags = {
    Environment = var.environment
    Service     = "stripe-alerts"
    Type        = "shared-infrastructure"
  }
}

resource "aws_sns_topic_subscription" "email_alerts" {
  count     = var.create_sns_topic && var.notification_email != "" ? 1 : 0
  topic_arn = aws_sns_topic.stripe_alerts[0].arn
  protocol  = "email"
  endpoint  = var.notification_email
}

# Outputs for other Terraform configurations to reference
output "secret_arn" {
  description = "ARN of the Stripe secrets"
  value       = module.stripe_secrets.secret_arn
}

output "secret_name" {
  description = "Name of the Stripe secrets"
  value       = module.stripe_secrets.secret_name
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for accessing secrets"
  value       = module.stripe_secrets.iam_policy_arn
}

output "sns_topic_arn" {
  description = "ARN of the SNS topic for alerts"
  value       = var.create_sns_topic ? aws_sns_topic.stripe_alerts[0].arn : null
}

# Usage instructions for other teams/environments
output "usage_instructions" {
  description = "How to use these secrets in other Terraform configurations"
  value       = <<-EOT
    
    📋 Using Stripe Secrets in Other Terraform Configurations:
    
    # In your Lambda Terraform configuration, add:
    environment {
      variables = {
        STRIPE_SECRETS_NAME = "${module.stripe_secrets.secret_name}"
      }
    }
    
    # Attach IAM policy to Lambda roles:
    resource "aws_iam_role_policy_attachment" "stripe_secrets" {
      role       = aws_iam_role.your_lambda_role.name
      policy_arn = "${module.stripe_secrets.iam_policy_arn}"
    }
    
    # For remote state reference:
    data "terraform_remote_state" "shared_secrets" {
      backend = "local"  # or "s3" if using remote backend
      config = {
        path = "../shared/secrets-manager/terraform.tfstate"
      }
    }
    
    # Then reference:
    STRIPE_SECRETS_NAME = data.terraform_remote_state.shared_secrets.outputs.secret_name
    
  EOT
}

output "rotation_instructions" {
  description = "Instructions for rotating Stripe keys"
  sensitive   = true
  value       = <<-EOT
    
    🔄 Stripe Key Rotation Instructions:
    
    1. Generate new key in Stripe Dashboard (requires 2FA)
    
    2. Update the secret:
    aws secretsmanager update-secret \
      --secret-id "${module.stripe_secrets.secret_name}" \
      --secret-string '{
        "STRIPE_SECRET_KEY": "sk_live_NEW_KEY",
        "STRIPE_ENDPOINT_SECRET": "${var.stripe_endpoint_secret}",
        "STRIPE_PUBLIC_KEY": "pk_live_NEW_KEY",
        "ROTATION_TIMESTAMP": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'",
        "VERSION": "2.0"
      }'
    
    3. Verify update:
    aws secretsmanager get-secret-value --secret-id "${module.stripe_secrets.secret_name}"
    
    4. All Lambda functions will automatically pick up the new key within 5 minutes
    
  EOT
}
