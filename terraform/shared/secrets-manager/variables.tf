variable "environment" {
  type        = string
  description = "Environment name (prod, staging, dev)"
  default     = "prod"

  validation {
    condition     = contains(["prod", "staging", "dev"], var.environment)
    error_message = "Environment must be one of: prod, staging, dev."
  }
}

variable "aws_region" {
  type        = string
  description = "AWS region for deployment"
  default     = "us-west-2"
}

variable "stripe_secret_key" {
  type        = string
  description = "Current Stripe secret key"
  sensitive   = true
}

variable "stripe_endpoint_secret" {
  type        = string
  description = "Current Stripe webhook endpoint secret"
  sensitive   = true
}

variable "stripe_public_key" {
  type        = string
  description = "Stripe publishable key"
  default     = ""
}

variable "rotation_days" {
  type        = number
  description = "Number of days between rotations (for monitoring)"
  default     = 7

  validation {
    condition     = var.rotation_days >= 1 && var.rotation_days <= 365
    error_message = "Rotation days must be between 1 and 365."
  }
}

# Cross-region replication variables removed due to AWS provider limitations

variable "create_sns_topic" {
  type        = bool
  description = "Create SNS topic for rotation notifications"
  default     = true
}

variable "notification_email" {
  type        = string
  description = "Email address for rotation notifications"
  default     = ""
}
