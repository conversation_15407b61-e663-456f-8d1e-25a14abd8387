variable "aws_region" {
  type        = string
  description = "AWS region for deployment"
  default     = "us-west-2"
}

variable "stripe_secret_key" {
  type        = string
  description = "Current Stripe secret key (use test key for dev environment)"
  sensitive   = true
}

variable "stripe_endpoint_secret" {
  type        = string
  description = "Current Stripe webhook endpoint secret"
  sensitive   = true
}

variable "stripe_public_key" {
  type        = string
  description = "Stripe publishable key (use test key for dev environment)"
  default     = ""
}

variable "create_sns_topic" {
  type        = bool
  description = "Create SNS topic for notifications"
  default     = true
}

variable "notification_email" {
  type        = string
  description = "Email address for rotation notifications"
  default     = ""
}
