# Development/Test Environment for Stripe Secrets Manager
# This is completely separate from production and safe to experiment with

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Use the secrets manager module
module "stripe_secrets" {
  source = "../modules/secrets-manager"
  
  environment             = "dev-test"  # Different from production
  stripe_secret_key      = var.stripe_secret_key
  stripe_endpoint_secret = var.stripe_endpoint_secret
  stripe_public_key      = var.stripe_public_key
  rotation_days          = 7
  
  # No cross-region replication for dev/test
  replica_regions = []
  
  common_tags = {
    Environment = "dev-test"
    Project     = "TheAlpineStudio"
    Service     = "stripe"
    ManagedBy   = "terraform"
    Purpose     = "testing-secrets-manager"
  }
  
  # Create SNS topic for testing notifications
  alarm_sns_topics = var.create_sns_topic ? [aws_sns_topic.stripe_alerts[0].arn] : []
}

# Optional: Create SNS topic for notifications
resource "aws_sns_topic" "stripe_alerts" {
  count = var.create_sns_topic ? 1 : 0
  name  = "dev-test-stripe-key-rotation-alerts"  # Different name from production
  
  tags = {
    Environment = "dev-test"
    Service     = "stripe-alerts"
    Purpose     = "testing"
  }
}

resource "aws_sns_topic_subscription" "email_alerts" {
  count     = var.create_sns_topic && var.notification_email != "" ? 1 : 0
  topic_arn = aws_sns_topic.stripe_alerts[0].arn
  protocol  = "email"
  endpoint  = var.notification_email
}

# Outputs
output "secret_arn" {
  description = "ARN of the Stripe secrets"
  value       = module.stripe_secrets.secret_arn
}

output "secret_name" {
  description = "Name of the Stripe secrets"
  value       = module.stripe_secrets.secret_name
}

output "iam_policy_arn" {
  description = "ARN of the IAM policy for accessing secrets"
  value       = module.stripe_secrets.iam_policy_arn
}

output "test_commands" {
  description = "Commands to test the secret"
  value = <<-EOT
    
    🧪 Test Commands:
    
    # Test secret retrieval:
    aws secretsmanager get-secret-value --secret-id "${module.stripe_secrets.secret_name}"
    
    # Test key rotation:
    aws secretsmanager update-secret --secret-id "${module.stripe_secrets.secret_name}" --secret-string '{"STRIPE_SECRET_KEY":"sk_test_NEW_KEY","STRIPE_ENDPOINT_SECRET":"whsec_test","STRIPE_PUBLIC_KEY":"pk_test","ROTATION_TIMESTAMP":"$(date -u +%Y-%m-%dT%H:%M:%SZ)","VERSION":"2.0"}'
    
    # For Lambda testing, use:
    STRIPE_SECRETS_NAME=${module.stripe_secrets.secret_name}
    
  EOT
}

output "production_integration_steps" {
  description = "Steps to integrate with production when ready"
  value = <<-EOT
    
    📋 When ready to integrate with production:
    
    1. Test this dev environment thoroughly
    2. Add the secrets manager module to your production terraform
    3. Add STRIPE_SECRETS_NAME environment variable to production Lambdas
    4. Attach IAM policy: ${module.stripe_secrets.iam_policy_arn}
    5. Test production Lambdas work with both old and new approaches
    6. Remove old STRIPE_SECRET_KEY environment variables when confident
    
  EOT
}
