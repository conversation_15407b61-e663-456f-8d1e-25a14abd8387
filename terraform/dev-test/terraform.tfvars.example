# Development/Test Environment Configuration
# Use Stripe TEST keys here, not production keys!

aws_region = "us-west-2"

# Use your Stripe TEST keys for safe experimentation
stripe_secret_key      = "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL"
stripe_endpoint_secret = "whsec_test_endpoint_secret"
stripe_public_key      = "pk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL"

# Optional: Get email notifications when testing
create_sns_topic    = true
notification_email  = "<EMAIL>"
