output "api_gateway_url" {
  description = "URL of the API Gateway"
  value       = module.api_gateway.api_gateway_url
}
output "api_gateway_arn" {
  description = "ARN of the API Gateway"
  value       = module.api_gateway.api_gateway_arn
}
output "cognito_user_pool_id" {
  value = module.cognito.cognito_user_pool_id
}
output "cognito_client_id" {
  value = module.cognito.user_pool_client_id
}
output "cognito_provider_name" {
  value = module.cognito.cognito_provider_name
}
output "bucket_name" {
  description = "Name (id) of the bucket"
  value       = module.website_s3_bucket.bucket_name
}
output "cognito_app_client_id" {
  description = "The ID of the Cognito User Pool Client"
  value       = module.cognito.user_pool_client_id
}
output "lambda_function_arn" {
  value = module.lambda_function.lambda_function_arn
}
output "api_gateway_id" {
 value =  module.api_gateway.api_gateway_id
}


# output "bucket_name" {
#   description = "Name (id) of the bucket"
#   value       = module.website_s3_bucket.bucket_name
# }

