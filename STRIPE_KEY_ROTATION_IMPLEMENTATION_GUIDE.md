# Stripe API Key Rotation Implementation Guide

## Overview
This guide implements a production-grade solution for managing Stripe API keys using AWS Secrets Manager, addressing the 7-day rotation requirement while maintaining zero-downtime operations.

## Current State Analysis
- **Problem**: Stripe API keys expire every 7 days and require manual 2FA rotation
- **Current Setup**: Hardcoded keys in `terraform.tfvars` files
- **Risk**: Manual process prone to errors and service disruption

## Solution Architecture

### Phase 1: AWS Secrets Manager Infrastructure ✅ COMPLETED
- Created Terraform module for Secrets Manager (`terraform/modules/secrets-manager/`)
- Supports multi-environment isolation
- Includes cross-region replication for disaster recovery
- Built-in monitoring and alerting

### Phase 2: Intelligent Key Management Library ✅ COMPLETED
- Created `lambda/shared/stripe_key_manager.go` with:
  - Automatic caching with 5-minute TTL
  - Retry logic for authentication failures
  - Fallback to previous key during rotation
  - Thread-safe operations

### Phase 3: Semi-Automated Rotation Process ✅ COMPLETED
- Created `lambda/stripeKeyRotation/main.go` for:
  - Monitoring rotation schedules
  - Sending notifications when rotation is needed
  - Updating secrets when new keys are provided
  - Zero-downtime key transitions

## Implementation Steps

### Step 1: Deploy Secrets Manager Infrastructure

1. **Update your test environment Terraform:**

```hcl
# In terraform/testing/test-env/main.tf, add:
module "stripe_secrets" {
  source = "../../modules/secrets-manager"
  
  environment             = "test"
  stripe_secret_key      = var.stripe_secret_key
  stripe_endpoint_secret = var.stripe_endpoint_secret
  stripe_public_key      = var.stripe_public_key
  rotation_days          = 7
  replica_regions        = ["us-east-1"] # Optional: for disaster recovery
  
  common_tags = {
    Environment = "test"
    Project     = "TheAlpineStudio"
    Service     = "stripe"
  }
  
  alarm_sns_topics = [aws_sns_topic.alerts.arn] # Create SNS topic for alerts
}
```

2. **Add to your test environment variables:**

```hcl
# In terraform/testing/test-env/variables.tf, add:
variable "stripe_public_key" {
  type        = string
  description = "Stripe publishable key"
  default     = ""
}
```

3. **Update your terraform.tfvars:**

```hcl
# Add this line to terraform/testing/test-env/terraform.tfvars:
stripe_public_key = "pk_live_..." # Your Stripe publishable key
```

### Step 2: Update Lambda Functions

For each Lambda function that uses Stripe (checkoutProducts, webhooks, etc.):

1. **Add environment variable in Terraform:**

```hcl
# In your Lambda Terraform configuration, add:
environment {
  variables = {
    STRIPE_SECRETS_NAME = module.stripe_secrets.secret_name
    # Remove STRIPE_SECRET_KEY and STRIPE_ENDPOINT_SECRET
  }
}
```

2. **Update Lambda IAM role:**

```hcl
# Attach the secrets access policy to your Lambda role:
resource "aws_iam_role_policy_attachment" "lambda_stripe_secrets" {
  role       = aws_iam_role.lambda_execution_role.name
  policy_arn = module.stripe_secrets.iam_policy_arn
}
```

### Step 3: Update Application Code

Replace the current Stripe key management in your Lambda functions:

**Before (current approach):**
```go
stripeKey := os.Getenv("STRIPE_SECRET_KEY")
stripe.Key = stripeKey
```

**After (new approach):**
```go
// Copy the StripeKeyManager code from lambda/shared/stripe_key_manager.go
// into each Lambda function (until we set up proper Go modules)

secretName := os.Getenv("STRIPE_SECRETS_NAME")
if secretName != "" {
    keyManager := NewStripeKeyManager(secretName)
    
    // Use the key manager for Stripe operations
    result, err := keyManager.ExecuteStripeOperation(func() (interface{}, error) {
        return paymentintent.New(params)
    })
}
```

### Step 4: Deploy Rotation Lambda

1. **Deploy the rotation Lambda function:**

```bash
cd lambda/stripeKeyRotation
GOOS=linux GOARCH=amd64 go build -o bootstrap main.go
zip stripe-key-rotation.zip bootstrap
aws s3 cp stripe-key-rotation.zip s3://your-lambda-bucket/
```

2. **Create Terraform for rotation Lambda:**

```hcl
resource "aws_lambda_function" "stripe_key_rotation" {
  function_name = "stripe-key-rotation"
  s3_bucket     = "your-lambda-bucket"
  s3_key        = "stripe-key-rotation.zip"
  role          = aws_iam_role.rotation_lambda_role.arn
  handler       = "bootstrap"
  runtime       = "provided.al2"
  
  environment {
    variables = {
      SNS_TOPIC_ARN = aws_sns_topic.stripe_alerts.arn
    }
  }
}
```

### Step 5: Set Up Monitoring and Alerts

1. **Create SNS topic for alerts:**

```hcl
resource "aws_sns_topic" "stripe_alerts" {
  name = "stripe-key-rotation-alerts"
}

resource "aws_sns_topic_subscription" "email_alerts" {
  topic_arn = aws_sns_topic.stripe_alerts.arn
  protocol  = "email"
  endpoint  = "<EMAIL>"
}
```

2. **Set up CloudWatch Events for rotation schedule:**

```hcl
resource "aws_cloudwatch_event_rule" "stripe_rotation_schedule" {
  name                = "stripe-key-rotation-schedule"
  description         = "Trigger Stripe key rotation check"
  schedule_expression = "rate(1 day)" # Check daily
}

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.stripe_rotation_schedule.name
  target_id = "StripeRotationLambda"
  arn       = aws_lambda_function.stripe_key_rotation.arn
}
```

## Operational Workflow

### Automated Process:
1. **Daily Check**: CloudWatch Events triggers rotation Lambda daily
2. **Notification**: When keys are 6 days old, you receive an email alert
3. **Manual Step**: You log into Stripe, generate new key (with 2FA)
4. **API Call**: You call the rotation API with the new key
5. **Automatic Update**: All Lambda functions automatically pick up the new key
6. **Zero Downtime**: Previous key remains valid during transition

### Manual Rotation API:

```bash
# When you get a rotation alert, call this API:
curl -X POST https://your-api-gateway/rotate-stripe-key \
  -H "Content-Type: application/json" \
  -d '{
    "secret_arn": "arn:aws:secretsmanager:us-west-2:123456789012:secret:test/stripe/api-keys",
    "new_secret_key": "sk_live_NEW_KEY_HERE",
    "environment": "test"
  }'
```

## Benefits of This Approach

1. **Security**: Keys stored securely in AWS Secrets Manager
2. **Zero Downtime**: Graceful key transitions with fallback support
3. **Automation**: Minimal manual intervention required
4. **Monitoring**: Comprehensive alerting and audit trails
5. **Scalability**: Works across multiple environments and regions
6. **Compliance**: Full audit trail of key access and rotations

## Next Steps

1. Start with the test environment
2. Deploy Secrets Manager infrastructure
3. Update one Lambda function as a proof of concept
4. Test the rotation process
5. Roll out to all Lambda functions
6. Deploy to staging and production environments

## Emergency Procedures

If a key is compromised:
1. Immediately generate a new key in Stripe
2. Call the rotation API with the new key
3. The old key will be automatically invalidated
4. All services will seamlessly transition to the new key

This implementation provides enterprise-grade security while maintaining operational simplicity.
