package main

import (
	"context"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"math/big"
	"net/http"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/dgrijalva/jwt-go"
)

var (
	region     = os.Getenv("AWS_REGION")
	userPoolID = os.Getenv("USER_POOL_ID") // Ensure USER_POOL_ID is set correctly
)

type JWK struct {
	Keys []json.RawMessage `json:"keys"`
}

// This is a custom authorizer for AWS API Gateway that verifies JWT tokens issued by AWS Cognito.
func generatePolicy(principalID, effect, methodArn string) events.APIGatewayCustomAuthorizerPolicy {
	return events.APIGatewayCustomAuthorizerPolicy{
		Version: "2012-10-17",
		Statement: []events.IAMPolicyStatement{
			{
				Action:   []string{"execute-api:Invoke"},
				Effect:   effect,
				Resource: []string{methodArn}, // Use the methodArn dynamically
			},
		},
	}
}

// Helper to get public key from JWK
func getPublicKeyFromJWK(token *jwt.Token, jwk *JWK) (*rsa.PublicKey, error) {
	for _, key := range jwk.Keys {
		var rsaKey map[string]interface{}
		if err := json.Unmarshal(key, &rsaKey); err != nil {
			return nil, fmt.Errorf("failed to unmarshal JWK key: %v", err)
		}

		kid, ok := rsaKey["kid"].(string)
		if !ok {
			continue // Skip if 'kid' is not present
		}

		if kid == token.Header["kid"] {
			nStr, ok := rsaKey["n"].(string)
			if !ok {
				return nil, fmt.Errorf("'n' parameter missing or not a string")
			}
			eStr, ok := rsaKey["e"].(string)
			if !ok {
				return nil, fmt.Errorf("'e' parameter missing or not a string")
			}

			// Decode 'n' from base64url
			nBytes, err := base64.RawURLEncoding.DecodeString(nStr)
			if err != nil {
				return nil, fmt.Errorf("failed to decode 'n': %v", err)
			}
			n := new(big.Int).SetBytes(nBytes)

			// Decode 'e' from base64url
			eBytes, err := base64.RawURLEncoding.DecodeString(eStr)
			if err != nil {
				return nil, fmt.Errorf("failed to decode 'e': %v", err)
			}

			// Convert eBytes to int
			e := 0
			for _, b := range eBytes {
				e = (e << 8) + int(b)
			}

			pubKey := &rsa.PublicKey{
				N: n,
				E: e,
			}
			log.Println("Public Key:", pubKey)

			return pubKey, nil
		}
	}
	return nil, fmt.Errorf("public key not found")
}

// Helper to get Cognito JWK
func getCognitoJWK(jwksURL string) (*JWK, error) {
	resp, err := http.Get(jwksURL)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var jwk JWK
	if err := json.NewDecoder(resp.Body).Decode(&jwk); err != nil {
		return nil, err
	}
	return &jwk, nil
}

// Function to verify JWT token
func verifyJWTToken(tokenString, jwksURL string) (*jwt.MapClaims, error) {
	jwk, err := getCognitoJWK(jwksURL)
	if err != nil {
		log.Printf("Failed to fetch JWK: %v", err)
		return nil, fmt.Errorf("Unauthorized")
	}
	log.Println("Fetched JWK:", jwk)

	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return getPublicKeyFromJWK(token, jwk)
	})

	if err != nil {
		return nil, fmt.Errorf("token verification failed: %v", err)
	}

	header, _ := jwt.DecodeSegment(strings.Split(tokenString, ".")[0])
	log.Println("JWT Header:", header)

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return &claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// Main authorizer handler
// Main authorizer handler
func tokenAuthorizerHandler(ctx context.Context, request events.APIGatewayCustomAuthorizerRequest) (events.APIGatewayCustomAuthorizerResponse, error) {
	token := request.AuthorizationToken
	if token == "" {
		log.Println("Authorization token is missing")
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("Unauthorized")
	}

	log.Println("Token to validate:", request.AuthorizationToken)

	// Strip "Bearer " from the token if present
	if len(token) > 7 && token[:7] == "Bearer " {
		token = token[7:]
	}

	// JWK URL for Cognito
	jwksURL := fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/%s/.well-known/jwks.json", region, userPoolID)

	// Verify the JWT token
	claims, err := verifyJWTToken(token, jwksURL)
	if err != nil {
		log.Printf("Token verification failed: %v", err)
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("Unauthorized")
	}

	// Extract the principal (user) from claims (e.g., sub or username)
	principalID, ok := (*claims)["sub"].(string)
	if !ok {
		log.Println("Failed to extract principal ID from token claims")
		return events.APIGatewayCustomAuthorizerResponse{}, fmt.Errorf("unauthorized (failed to extract principal ID and claims)")
	}

	// Generate policy using the helper function
	policy := generatePolicy(principalID, "Allow", request.MethodArn)

	// Return the custom authorizer response with the generated policy
	return events.APIGatewayCustomAuthorizerResponse{
		PrincipalID:    principalID,
		PolicyDocument: policy,
	}, nil
}

func main() {
	lambda.Start(tokenAuthorizerHandler)
}
