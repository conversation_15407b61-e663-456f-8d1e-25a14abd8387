package main

import (
    "context"
    "log"

    "github.com/aws/aws-lambda-go/lambda"
)

type DefineAuthChallengeRequest struct {
    Request struct {
        UserAttributes map[string]string `json:"userAttributes"`
        Session        []struct {
            ChallengeName    string `json:"challengeName"`
            ChallengeResult  bool   `json:"challengeResult"`
            ChallengeMetadata string `json:"challengeMetadata"`
        } `json:"session"`
    } `json:"request"`
    Response struct {
        ChallengeName        string `json:"challengeName"`
        IssueTokens          bool   `json:"issueTokens"`
        FailAuthentication   bool   `json:"failAuthentication"`
        ChallengeMetadata    string `json:"challengeMetadata"`
    } `json:"response"`
}

func handleDefineAuthChallenge(ctx context.Context, event DefineAuthChallengeRequest) (DefineAuthChallengeRequest, error) {
    log.Printf("Received event: %+v", event)

    sessionLen := len(event.Request.Session)

    // Always set the challenge if there are no previous sessions or if the previous challenge was successful
    if sessionLen == 0 || (sessionLen > 0 && event.Request.Session[sessionLen-1].ChallengeResult == true) {
        event.Response.ChallengeName = "CUSTOM_CHALLENGE"
        event.Response.IssueTokens = false
        event.Response.FailAuthentication = false
    } else {
        // If previous challenge was not successful, fail the authentication
        event.Response.ChallengeName = "CUSTOM_CHALLENGE"
        event.Response.IssueTokens = false
        event.Response.FailAuthentication = true
    }

    log.Printf("Returning response: %+v", event)
    return event, nil
}

func main() {
    lambda.Start(handleDefineAuthChallenge)
}
