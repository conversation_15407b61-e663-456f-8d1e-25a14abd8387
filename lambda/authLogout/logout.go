package main

import (
    "context"
    "encoding/json"
    "log"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

type LogoutRequest struct {
    Token string `json:"token"`
}

var (
    region = os.Getenv("AWS_REGION")
)

func handleLogout(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Println("Request method:", request.HTTPMethod)

    if request.HTTPMethod == "OPTIONS" {
        return events.APIGatewayProxyResponse{Headers: addCORSHeaders(), StatusCode: 200}, nil
    }

    var req LogoutRequest
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return responseWithError("Invalid request body", 400)
    }

    sess, err := session.NewSession(&aws.Config{Region: aws.String(region)})
    if err != nil {
        return responseWithError("Failed to create AWS session", 500)
    }

    svc := cognitoidentityprovider.New(sess)
    params := &cognitoidentityprovider.GlobalSignOutInput{
        AccessToken: aws.String(req.Token),
    }

    _, err = svc.GlobalSignOut(params)
    if err != nil {
        return responseWithError("Failed to logout: "+err.Error(), 500)
    }

    return events.APIGatewayProxyResponse{
        Headers:    addCORSHeaders(),
        StatusCode: 200,
        Body:       jsonMustMarshal(map[string]string{"message": "Logout successful"}),
    }, nil
}

func addCORSHeaders() map[string]string {
    return map[string]string{
        "Access-Control-Allow-Origin":      "*",
        "Access-Control-Allow-Methods":     "POST, OPTIONS",
        "Access-Control-Allow-Headers":     "Content-Type, Authorization",
        "Access-Control-Allow-Credentials": "true",
    }
}

func responseWithError(message string, statusCode int) (events.APIGatewayProxyResponse, error) {
    return events.APIGatewayProxyResponse{
        Body:       jsonMustMarshal(map[string]string{"message": message}),
        StatusCode: statusCode,
        Headers:    addCORSHeaders(),
    }, nil
}

func jsonMustMarshal(data interface{}) string {
    jsonBytes, err := json.Marshal(data)
    if err != nil {
        log.Fatalf("JSON marshalling failed: %s", err)
    }
    return string(jsonBytes)
}

func main() {
    lambda.Start(handleLogout)
}
