package main

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/base64"
    "fmt"
    "log"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
)

var (
    allowedEmails = map[string]bool{
        "<EMAIL>":      true,
        "<EMAIL>": true,
    }
    allowedPhoneNumbers = map[string]bool{
        "+17325985266": true,
        "+13315757436": true,
    }
    clientID     = os.Getenv("CLIENT_ID")
    clientSecret = os.Getenv("CLIENT_SECRET")
)

func generateSecretHash(username, clientID, clientSecret string) string {
    message := username + clientID
    h := hmac.New(sha256.New, []byte(clientSecret))
    h.Write([]byte(message))
    return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func preSignUpHandler(ctx context.Context, event events.CognitoEventUserPoolsPreSignup) (events.CognitoEventUserPoolsPreSignup, error) {
    log.Println("Pre sign-up event triggered")
    log.Printf("Event: %+v", event)

    email := event.Request.UserAttributes["email"]
    phoneNumber := event.Request.UserAttributes["phone_number"]
    username := event.UserName
    log.Printf("Checking if email %s and phone number %s are allowed to sign up", email, phoneNumber)

    if !allowedEmails[email] && !allowedPhoneNumbers[phoneNumber] {
        errMsg := fmt.Sprintf("User with email %s or phone number %s is not allowed to sign up", email, phoneNumber)
        log.Println(errMsg)
        return event, fmt.Errorf(errMsg)
    }

    log.Printf("Email %s and phone number %s are allowed", email, phoneNumber)

    secretHash := generateSecretHash(username, clientID, clientSecret)
    log.Printf("Generated secret hash: %s", secretHash)

    // Logging the event response for verification
    log.Printf("Event Response before modification: %+v", event.Response)

    // No auto-confirmation
    event.Response.AutoConfirmUser = false
    event.Response.AutoVerifyEmail = false
    event.Response.AutoVerifyPhone = false

    // Logging the modified event response
    log.Printf("Event Response after modification: %+v", event.Response)

    // Simulating the user creation process for logging purposes
    log.Println("Simulating user creation process")
    log.Printf("User attributes: email=%s, phone_number=%s, username=%s", email, phoneNumber, username)

    log.Println("Pre sign-up event processed successfully")
    return event, nil
}

func main() {
    lambda.Start(preSignUpHandler)
}

