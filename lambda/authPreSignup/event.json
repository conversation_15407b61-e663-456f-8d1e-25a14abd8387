
  

  {
    "version": "1",
    "triggerSource": "PreSignUp_SignUp",
    "region": "us-west-2",
    "userPoolId": "us-west-2_GOD2mBQiC",
    "userName": "testuser",
    "callerContext": {
      "awsSdkVersion": "aws-sdk-unknown-unknown",
      "clientId": "6ej9p4miap6snoncgrj3kjtme"
    },
    "request": {
      "userAttributes": {
        "email": "<EMAIL>",
        "phone_number": "+17325985266",
        "name": "Test User",
        "password": "UserProvidedPassword123!"
      },
      "validationData": {}
    },
    "response": {
      "autoConfirmUser": false,
      "autoVerifyEmail": false,
      "autoVerifyPhone": false
    }
  }
  




  {
    "ClientId": "6ej9p4miap6snoncgrj3kjtme",
    "ClientSecret": "10nkkdu898itqfjhg55oo86ksbe62abjemcci70av83p6afhg0kr",
    "body": "{\"email\": \"<EMAIL>\", \"password\": \"UserProvidedPassword123!\", \"name\": \"Test User\", \"phone_number\": \"+17325985266\"}"
  }
  