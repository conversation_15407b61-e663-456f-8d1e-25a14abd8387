package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"github.com/resendlabs/resend-go"
	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/webhook"
)

// Constants for email details
const (
	Sender       = "<EMAIL>"
	AdminEmail   = "<EMAIL>"
	SubjectUser  = "Order Confirmation"
	SubjectAdmin = "New Order Received"
)

// Order structure matching DynamoDB schema
type Order struct {
	OrderID         string `json:"order_id" dynamodbav:"order_id"`
	UserEmail       string `json:"user_email" dynamodbav:"user_email"`
	AmountTotal     int    `json:"amount_total" dynamodbav:"amount_total"`
	Currency        string `json:"currency" dynamodbav:"currency"`
	ShippingDetails struct {
		Name    string `json:"name" dynamodbav:"name"`
		Address struct {
			Line1      string `json:"line1" dynamodbav:"line1"`
			Line2      string `json:"line2" dynamodbav:"line2"`
			City       string `json:"city" dynamodbav:"city"`
			State      string `json:"state" dynamodbav:"state"`
			PostalCode string `json:"postal_code" dynamodbav:"postal_code"`
			Country    string `json:"country" dynamodbav:"country"`
		} `json:"address" dynamodbav:"address"`
	} `json:"shipping_details"`
	Products []Product `json:"products" dynamodbav:"products"`
}

// Product structure matching DynamoDB schema
type Product struct {
	ID    string `json:"id" dynamodbav:"id"`
	Title string `json:"title" dynamodbav:"title"`
	Size  string `json:"size" dynamodbav:"size"`
	Price int    `json:"price" dynamodbav:"price"`
}

// Handle webhook
func handleWebhook(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	stripe.Key = os.Getenv("STRIPE_SECRET_KEY")
	endpointSecret := os.Getenv("STRIPE_ENDPOINT_SECRET")

	log.Printf("Headers: %+v", request.Headers)
	log.Printf("Stripe-Signature Header: %s", request.Headers["Stripe-Signature"])

	// Verify the webhook signature
	signatureHeader := request.Headers["Stripe-Signature"]
	if signatureHeader == "" {
		log.Println("Missing Stripe-Signature header")
		return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Missing Stripe-Signature header"}, nil
	}

	event, err := webhook.ConstructEvent([]byte(request.Body), signatureHeader, endpointSecret)
	if err != nil {
		log.Printf("Error verifying webhook signature: %v\n", err)
		return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Webhook Error: Invalid signature."}, nil
	}

	if event.Type == "checkout.session.completed" {
		var session stripe.CheckoutSession
		err = json.Unmarshal(event.Data.Raw, &session)
		if err != nil {
			log.Printf("Failed to unmarshal checkout session: %v\n", err)
			return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Error processing event."}, nil
		}

		// Fetch order details from DynamoDB
		order, err := fetchOrder(session.ID)
		if err != nil {
			log.Printf("Failed to fetch order: %v\n", err)
			return events.APIGatewayProxyResponse{StatusCode: 500, Body: "Error fetching order details."}, nil
		}

		// Send emails
		sendEmails(order)
	}

	return events.APIGatewayProxyResponse{StatusCode: 200, Body: "Received"}, nil
}

// Fetch order details from DynamoDB
func fetchOrder(orderID string) (*Order, error) {
	tableName := os.Getenv("DYNAMODB_TABLE_ORDERS_NAME")
	region := os.Getenv("AWS_REGION")

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS session: %v", err)
	}

	dynamoClient := dynamodb.New(sess)
	key := map[string]*dynamodb.AttributeValue{
		"order_id": {
			S: aws.String(orderID),
		},
	}

	input := &dynamodb.GetItemInput{
		TableName: aws.String(tableName),
		Key:       key,
	}

	result, err := dynamoClient.GetItem(input)
	if err != nil {
		return nil, fmt.Errorf("failed to get item from DynamoDB: %v", err)
	}

	if result.Item == nil {
		return nil, fmt.Errorf("order not found for ID: %s", orderID)
	}

	var order Order
	err = dynamodbattribute.UnmarshalMap(result.Item, &order)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal order: %v", err)
	}

	return &order, nil
}

// Send emails to user and admin
func sendEmails(order *Order) {
	apiKey := os.Getenv("RESEND_API_KEY")
	client := resend.NewClient(apiKey)

	formattedAmount := fmt.Sprintf("$%.2f", float64(order.AmountTotal)/100.0)
	productListHTML := ""
	for _, product := range order.Products {
		productListHTML += fmt.Sprintf(
			`<li><strong>Product:</strong> %s, <strong>Size:</strong> %s, <strong>Price:</strong> $%.2f</li>`,
			product.Title,
			product.Size,
			float64(product.Price)/100.0,
		)
	}

	// User email content
	userEmailHTML := fmt.Sprintf(
		`<h2>Thank You for Your Order!</h2>
		<p><strong>Total Amount:</strong> %s</p>
		<ul>%s</ul>
		<p><strong>Shipping Address:</strong> %s, %s, %s, %s %s, %s</p>`,
		formattedAmount,
		productListHTML,
		order.ShippingDetails.Address.Line1,
		order.ShippingDetails.Address.Line2,
		order.ShippingDetails.Address.City,
		order.ShippingDetails.Address.State,
		order.ShippingDetails.Address.PostalCode,
		order.ShippingDetails.Address.Country,
	)

	// Admin email content
	adminEmailHTML := fmt.Sprintf(
		`<h2>New Order Received</h2>
		<p><strong>User Email:</strong> %s</p>
		<p><strong>Total Amount:</strong> %s</p>
		<ul>%s</ul>
		<p><strong>Shipping Address:</strong> %s, %s, %s, %s %s</p>`,
		order.UserEmail,
		formattedAmount,
		productListHTML,
		order.ShippingDetails.Address.Line1,
		order.ShippingDetails.Address.Line2,
		order.ShippingDetails.Address.City,
		order.ShippingDetails.Address.State,
		order.ShippingDetails.Address.PostalCode,
		order.ShippingDetails.Address.Country,
	)

	// Send emails
	_, userErr := client.Emails.Send(&resend.SendEmailRequest{
		From:    Sender,
		To:      []string{order.UserEmail},
		Subject: SubjectUser,
		Html:    userEmailHTML,
	})
	if userErr != nil {
		log.Printf("Failed to send user email: %v\n", userErr)
	}

	_, adminErr := client.Emails.Send(&resend.SendEmailRequest{
		From:    Sender,
		To:      []string{AdminEmail},
		Subject: SubjectAdmin,
		Html:    adminEmailHTML,
	})
	if adminErr != nil {
		log.Printf("Failed to send admin email: %v\n", adminErr)
	}
}

func main() {
	lambda.Start(handleWebhook)
}
