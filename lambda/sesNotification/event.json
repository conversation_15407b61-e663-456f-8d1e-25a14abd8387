{"Records": [{"EventVersion": "1.0", "EventSubscriptionArn": "example-arn", "EventSource": "aws:sns", "Sns": {"SignatureVersion": "1", "Timestamp": "1970-01-01T00:00:00.000Z", "Signature": "EXAMPLE", "SigningCertUrl": "EXAMPLE", "MessageId": "95df01b4-ee98-5cb9-9903-4c221d41eb5e", "Message": " ", "MessageAttributes": {"Test": {"Type": "String", "Value": "TestString"}, "TestBinary": {"Type": "Binary", "Value": "TestBinary"}}, "Type": "Notification", "UnsubscribeUrl": "EXAMPLE", "TopicArn": "example-arn", "Subject": "TestInvoke"}}]}