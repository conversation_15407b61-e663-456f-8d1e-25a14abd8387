{"version": "1", "triggerSource": "VerifyAuthChallengeResponse_Authentication", "region": "us-west-2", "userPoolId": "us-west-2_GOD2mBQiC", "userName": "jane<PERSON><PERSON>", "callerContext": {"awsSdkVersion": "aws-sdk-unknown-unknown", "clientId": "example_client_id"}, "request": {"userAttributes": {"sub": "example_sub", "email_verified": "true", "cognito:user_status": "CONFIRMED", "email": "<EMAIL>"}, "privateChallengeParameters": {"answer": "123456"}, "challengeAnswer": "123456"}, "response": {"answerCorrect": null}}