// ./backend/handlers/create_payment_intent.go

package main

import (
	"context"
	"encoding/json"
	"log"
	"os"
	"time"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
	"github.com/aws/aws-sdk-go/service/secretsmanager"
	"github.com/google/uuid"
	"github.com/stripe/stripe-go/v78"
	"github.com/stripe/stripe-go/v78/paymentintent"
)

type CheckoutEvent struct {
	Items           []Product       `json:"items"`
	CustomerEmail   string          `json:"customer_email"`
	ShippingDetails ShippingDetails `json:"shipping_details"`
}

type Product struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	SubCategory string  `json:"subcategory"`
	Size        string  `json:"size"`
	Currency    string  `json:"currency"`
	Quantity    int64   `json:"quantity"`
	Price       float64 `json:"price"` // in cents
	ImageURL    string  `json:"imageUrl"`
}

type ShippingDetails struct {
	Address Address `json:"address"`
	Name    string  `json:"name"`
}

type Address struct {
	Line1      string `json:"line1"`
	Line2      string `json:"line2"`
	City       string `json:"city"`
	State      string `json:"state"`
	PostalCode string `json:"postal_code"`
	Country    string `json:"country"`
}

type Order struct {
	OrderID         string          `json:"order_id"`
	CustomerEmail   string          `json:"customer_email"`
	AmountTotal     int64           `json:"amount_total"`
	Currency        string          `json:"currency"`
	ShippingDetails ShippingDetails `json:"shipping_details"`
	Products        []Product       `json:"products"`
	PaymentIntentID string          `json:"payment_intent_id"`
	Status          string          `json:"status"` // e.g., "Pending", "Succeeded"
	CreatedAt       string          `json:"created_at"`
	UserEmail       string          `json:"user_email"`
}

type Response struct {
	ClientSecret string `json:"client_secret,omitempty"`
	Error        string `json:"error,omitempty"`
}

// Simple helper function to get Stripe key from either Secrets Manager or environment variable
func getStripeKey() string {
	// Try Secrets Manager first
	secretName := os.Getenv("STRIPE_SECRETS_NAME")
	if secretName != "" {
		key := getKeyFromSecretsManager(secretName)
		if key != "" {
			return key
		}
		log.Println("Failed to get key from Secrets Manager, falling back to environment variable")
	}

	// Fallback to environment variable
	return os.Getenv("STRIPE_SECRET_KEY")
}

// Get key from AWS Secrets Manager
func getKeyFromSecretsManager(secretName string) string {
	sess, err := session.NewSession()
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)
		return ""
	}

	client := secretsmanager.New(sess)
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}

	result, err := client.GetSecretValue(input)
	if err != nil {
		log.Printf("Failed to get secret from AWS: %v", err)
		return ""
	}

	// Parse the JSON secret
	var secrets map[string]string
	if err := json.Unmarshal([]byte(*result.SecretString), &secrets); err != nil {
		log.Printf("Failed to parse secret JSON: %v", err)
		return ""
	}

	key, exists := secrets["STRIPE_SECRET_KEY"]
	if !exists {
		log.Println("STRIPE_SECRET_KEY not found in secret")
		return ""
	}

	log.Printf("Successfully retrieved Stripe key from Secrets Manager")
	return key
}

func handleCreatePaymentIntent(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Handle preflight OPTIONS request
	if request.HTTPMethod == "OPTIONS" {
		return events.APIGatewayProxyResponse{
			StatusCode: 200,
			Headers:    defaultHeaders(request),
			Body:       "",
		}, nil
	}

	// Retrieve environment variables
	stripeKey := getStripeKey()                       // Use new function that tries Secrets Manager first
	ordersTable := os.Getenv("DYNAMODB_ORDERS_TABLE") // Name of the Orders table

	if stripeKey == "" || ordersTable == "" {
		log.Println("Stripe secret key or DynamoDB Orders table name is not set.")
		responseBody, _ := json.Marshal(Response{Error: "Server configuration error."})
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Initialize Stripe
	stripe.Key = stripeKey

	// Initialize DynamoDB client
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String("us-west-2"), // Replace with your region
	})
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)
		responseBody, _ := json.Marshal(Response{Error: "Internal server error."})
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	svc := dynamodb.New(sess)

	// Parse the request body
	var event CheckoutEvent
	err = json.Unmarshal([]byte(request.Body), &event)
	if err != nil {
		log.Printf("Error parsing request body: %v", err)
		responseBody, _ := json.Marshal(Response{Error: "Invalid request body."})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Validate the event data
	if len(event.Items) == 0 {
		log.Println("No items provided in the checkout event.")
		responseBody, _ := json.Marshal(Response{Error: "No items to checkout."})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Calculate total amount
	var totalAmount int64 = 0
	for _, item := range event.Items {
		if item.Price < 0 || item.Quantity <= 0 {
			log.Printf("Invalid price or quantity for item ID %s", item.ID)
			responseBody, _ := json.Marshal(Response{Error: "Invalid item price or quantity."})
			return events.APIGatewayProxyResponse{
				StatusCode: 400,
				Body:       string(responseBody),
				Headers:    defaultHeaders(request),
			}, nil
		}
		// Convert dollars to cents
		itemAmountCents := int64(item.Price * 100)
		totalAmount += itemAmountCents * item.Quantity
	}

	if totalAmount <= 0 {
		log.Println("Total amount must be greater than zero.")
		responseBody, _ := json.Marshal(Response{Error: "Invalid total amount."})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Create a unique order ID
	orderID := uuid.New().String()

	// Create PaymentIntent
	piParams := &stripe.PaymentIntentParams{
		Amount:       stripe.Int64(totalAmount),
		ReceiptEmail: stripe.String(event.CustomerEmail),     // a human-readable summary stripe will show in Dashboard
		Description: stripe.String(fmt.Sprintf(
			"Order %s: %d items total, $%.2f",
			orderID, len(event.Items), float64(totalAmount)/100,
			
		)),
		// Shipping: &stripe.ShippingDetailsParams{
		// 	Name: stripe.String(event.ShippingDetails.Name),
		// 	Address: &stripe.AddressParams{
		// 	  Line1:      stripe.String(event.ShippingDetails.Address.Line1),
		// 	  Line2:      stripe.String(event.ShippingDetails.Address.Line2),
		// 	  City:       stripe.String(event.ShippingDetails.Address.City),
		// 	  State:      stripe.String(event.ShippingDetails.Address.State),
		// 	  PostalCode: stripe.String(event.ShippingDetails.Address.PostalCode),
		// 	  Country:    stripe.String(event.ShippingDetails.Address.Country),
		// 	},
		//   },		
		Currency: stripe.String("usd"),
		Metadata: map[string]string{
			"order_id": orderID,
		},
	}

	log.Printf("Using Stripe key: %s…", stripeKey[:8])

	// Note: Use `piParams` here, not `paymentIntentParams`
	pi, err := paymentintent.New(piParams)
	if err != nil {
		log.Printf("Failed to create PaymentIntent: %v", err)
		responseBody, _ := json.Marshal(Response{Error: "Failed to create PaymentIntent."})
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// If customer_email is required, ensure the frontend or caller always sends a valid email.
	if event.CustomerEmail == "" {
		// Return a 400 error asking the user to provide an email.
		responseBody, _ := json.Marshal(Response{Error: "A valid customer_email is required."})
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Create Order struct
	order := Order{
		OrderID:         orderID,
		CustomerEmail:   event.CustomerEmail,
		AmountTotal:     totalAmount,
		Currency:        "usd", // Set dynamically if needed
		ShippingDetails: event.ShippingDetails,
		Products:        event.Items,
		PaymentIntentID: pi.ID,
		Status:          "Pending",
		CreatedAt:       time.Now().Format(time.RFC3339),
		UserEmail:       event.CustomerEmail,
	}

	// Marshal order to DynamoDB attribute value
	av, err := dynamodbattribute.MarshalMap(order)
	if err != nil {
		log.Printf("Failed to marshal order to DynamoDB format: %v", err)
		responseBody, _ := json.Marshal(Response{Error: "Internal server error."})
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Put item into DynamoDB
	input := &dynamodb.PutItemInput{
		Item:      av,
		TableName: aws.String(ordersTable),
	}

	_, err = svc.PutItem(input)
	if err != nil {
		log.Printf("Failed to put order into DynamoDB: %v", err)
		responseBody, _ := json.Marshal(Response{Error: "Internal server error."})
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       string(responseBody),
			Headers:    defaultHeaders(request),
		}, nil
	}

	// Return clientSecret to frontend
	responseBody, _ := json.Marshal(Response{ClientSecret: pi.ClientSecret})
	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers:    defaultHeaders(request),
	}, nil
}

func defaultHeaders(request events.APIGatewayProxyRequest) map[string]string {
	origin := request.Headers["origin"]
	// Define a list of allowed origins.
	allowedOrigins := map[string]bool{
		"https://thealpinestudio.com":      true,
		"https://test.thealpinestudio.com": true,
	}

	if allowedOrigins[origin] {
		return map[string]string{
			"Content-Type":                     "application/json",
			"Access-Control-Allow-Origin":      origin,
			"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
			"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
			"Access-Control-Allow-Credentials": "true",
		}
	}
	// Fallback header if the origin is not allowed
	return map[string]string{
		"Content-Type":                     "application/json",
		"Access-Control-Allow-Origin":      "https://thealpinestudio.com",
		"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
		"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
		"Access-Control-Allow-Credentials": "true",
	}
}

func main() {
	lambda.Start(handleCreatePaymentIntent)
}
