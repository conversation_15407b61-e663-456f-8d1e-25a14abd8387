package main

import (
    "context"
    "encoding/json"
    "fmt"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
    "github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

type Product struct {
    ID          string   `json:"id" dynamodbav:"ID"`
    Title       string   `json:"title" dynamodbav:"Title"`
    Description string   `json:"description" dynamodbav:"Description"`
    Sizes       []Size   `json:"sizes" dynamodbav:"Sizes"`
    ImageURL    string   `json:"imageUrl" dynamodbav:"ImageUrl"`
    Category    string   `json:"category" dynamodbav:"Category"`
	Subcategories []string `json:"subcategories" dynamodbav:"subcategories"` // Plural
    Sold        bool     `json:"sold" dynamodbav:"Sold"`
}

type Size struct {
    Size  string `json:"size" dynamodbav:"Size"`
    Price struct {
        Amount   float64 `json:"amount" dynamodbav:"Amount"`
        Currency string  `json:"currency" dynamodbav:"Currency"`
    } `json:"price" dynamodbav:"Price"`
}

var (
    db        *dynamodb.DynamoDB
    tableName string
)

func init() {
    sess, err := session.NewSessionWithOptions(session.Options{
        SharedConfigState: session.SharedConfigEnable,
    })
    if err != nil {
        fmt.Println("Error creating session:", err)
        panic(err)
    }
    db = dynamodb.New(sess)
    tableName = os.Getenv("TABLE_NAME")
    if tableName == "" {
        fmt.Println("TABLE_NAME environment variable is not set")
        panic("TABLE_NAME environment variable is not set")
    }
}

func GetProductByIdHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    fmt.Println("Received request:", request)
    productID := request.PathParameters["id"]
    if productID == "" {
        fmt.Println("Product ID is missing in the request.")
        return generateErrorResponse(400, "Product ID is missing")
    }

    // Use Query operation
    input := &dynamodb.QueryInput{
        TableName:              aws.String(tableName),
        KeyConditionExpression: aws.String("#id = :id"),
        ExpressionAttributeNames: map[string]*string{
            "#id": aws.String("ID"),
        },
        ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
            ":id": {
                S: aws.String(productID),
            },
        },
    }

    fmt.Printf("Querying items with ID: %s from table: %s\n", productID, tableName)
    result, err := db.Query(input)
    if err != nil {
        fmt.Println("Error calling Query:", err.Error())
        return generateErrorResponse(500, "Internal Server Error")
    }

    if len(result.Items) == 0 {
        fmt.Printf("Product with ID %s not found.\n", productID)
        return generateErrorResponse(404, "Product not found")
    }

    // Assuming you want the first item returned
    product := Product{}
    err = dynamodbattribute.UnmarshalMap(result.Items[0], &product)
    if err != nil {
        fmt.Println("Failed to unmarshal DynamoDB item:", err)
        return generateErrorResponse(500, "Internal Server Error")
    }

    fmt.Printf("Unmarshalled product: %+v\n", product)
    body, err := json.Marshal(product)
    if err != nil {
        fmt.Println("Failed to marshal product to JSON:", err)
        return generateErrorResponse(500, "Internal Server Error")
    }

    fmt.Println("Successfully fetched and marshalled product.")
    return events.APIGatewayProxyResponse{
        StatusCode: 200,
        Body:       string(body),
        Headers: map[string]string{
            "Content-Type":                     "application/json",
            "Access-Control-Allow-Origin":      "*",
            "Access-Control-Allow-Methods":     "GET, OPTIONS",
            "Access-Control-Allow-Headers":     "Content-Type,Authorization",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

// Helper function to generate error responses
func generateErrorResponse(statusCode int, message string) (events.APIGatewayProxyResponse, error) {
    responseBody := fmt.Sprintf(`{"message": "%s"}`, message)
    return events.APIGatewayProxyResponse{
        StatusCode: statusCode,
        Body:       responseBody,
        Headers: map[string]string{
            "Content-Type":                     "application/json",
            "Access-Control-Allow-Origin":      "*",
            "Access-Control-Allow-Methods":     "GET, OPTIONS",
            "Access-Control-Allow-Headers":     "Content-Type,Authorization",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

func main() {
    lambda.Start(GetProductByIdHandler)
}
