AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: An AWS Lambda  function for refreshing a token.
Resources:
  refreshTokenHandler:
    Type: AWS::Serverless::Function
    Properties:
      Handler: auth-login.lambda_handler
      Runtime: go1.x
      CodeUri: .
      MemorySize: 256
      Timeout: 10
      Environment:
        Variables:
          CLIENT_ID: "6ej9p4miap6snoncgrj3kjtme"
          CLIENT_SECRET: "10nkkdu898itqfjhg55oo86ksbe62abjemcci70av83p6afhg0kr"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /refresh-token
            Method: post


