package main

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/base64"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

var (
    clientID     = os.Getenv("CLIENT_ID")
    clientSecret = os.Getenv("CLIENT_SECRET")
    region       = os.Getenv("AWS_REGION")
)

// generateSecretHash creates an HMAC SHA-256 hash using the client secret
func generateSecretHash(email, clientID, clientSecret string) string {
    message := email + clientID
    h := hmac.New(sha256.New, []byte(clientSecret))
    h.Write([]byte(message))
    return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

type RefreshTokenRequest struct {
    Email        string `json:"email"`
    RefreshToken string `json:"refreshToken"`
}

type RefreshTokenResponse struct {
    AccessToken string `json:"accessToken"`
    IdToken     string `json:"idToken"`
    ExpiresIn   int    `json:"expiresIn"`
    Message     string `json:"message"`
}

// handleRefreshToken handles the refresh token request
func handleRefreshToken(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Println("Received refresh token request")

    var req RefreshTokenRequest
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        log.Printf("Invalid request: %v", err)
        return buildResponse(http.StatusBadRequest, fmt.Sprintf("Invalid request: %v", err))
    }

    log.Printf("Parsed request: Email=%s, RefreshToken=<token hidden for security>", req.Email)

    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return buildResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to create AWS session: %v", err))
    }

    svc := cognitoidentityprovider.New(sess)

    authInput := &cognitoidentityprovider.InitiateAuthInput{
        AuthFlow: aws.String("REFRESH_TOKEN_AUTH"),
        AuthParameters: map[string]*string{
            "REFRESH_TOKEN": aws.String(req.RefreshToken),
            "SECRET_HASH":   aws.String(generateSecretHash(req.Email, clientID, clientSecret)),
        },
        ClientId: aws.String(clientID),
    }

    log.Printf("Initiating auth with Cognito for email: %s", req.Email)

    authResult, err := svc.InitiateAuth(authInput)
    if err != nil {
        log.Printf("Error refreshing token: %v", err)
        return buildResponse(http.StatusUnauthorized, fmt.Sprintf("Error refreshing token: %v", err))
    }

    log.Println("Successfully refreshed token")

    response := RefreshTokenResponse{
        Message: "Token refreshed successfully",
    }

    if authResult.AuthenticationResult != nil {
        if authResult.AuthenticationResult.AccessToken != nil {
            response.AccessToken = *authResult.AuthenticationResult.AccessToken
        }
        if authResult.AuthenticationResult.IdToken != nil {
            response.IdToken = *authResult.AuthenticationResult.IdToken
        }
        if authResult.AuthenticationResult.ExpiresIn != nil {
            response.ExpiresIn = int(*authResult.AuthenticationResult.ExpiresIn)
        }
    }

    responseBody, err := json.Marshal(response)
    if err != nil {
        log.Printf("Failed to marshal response: %v", err)
        return buildResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to marshal response: %v", err))
    }

    return buildResponse(http.StatusOK, string(responseBody))
}

// buildResponse constructs an APIGatewayProxyResponse with CORS headers
func buildResponse(statusCode int, body string) (events.APIGatewayProxyResponse, error) {
    return events.APIGatewayProxyResponse{
        StatusCode: statusCode,
        Body:       body,
        Headers: map[string]string{
            "Access-Control-Allow-Origin":      "*",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

func main() {
    lambda.Start(handleRefreshToken)
}
