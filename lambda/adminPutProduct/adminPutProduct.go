package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// Product represents the product structure with all editable fields
type Product struct {
	ID            string   `json:"id,omitempty"`
	Title         string   `json:"title,omitempty"`
	Description   string   `json:"description,omitempty"`
	Sizes         []Size   `json:"sizes,omitempty"`
	Prices        []Price  `json:"prices,omitempty"`
	ImageUrl      string   `json:"imageUrl,omitempty"`
	Category      string   `json:"category,omitempty"`
	Subcategories []string `json:"subcategories,omitempty"`
	Sold          bool     `json:"sold,omitempty"`
}

// Size represents the size structure
type Size struct {
	Size  string `json:"size"`
	Price Price  `json:"price"`
}

// Price represents the price structure
type Price struct {
	Amount   float64 `json:"amount"`
	Currency string  `json:"currency"`
}

// Helper function to join strings with a separator
func join(strs []string, sep string) string {
	return strings.Join(strs, sep)
}

// HandleRequest handles the update product request
func AdminEditProductHandleRequest(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Printf("Raw request body: %s", request.Body)
	log.Printf("IsBase64Encoded: %v", request.IsBase64Encoded)

	if strings.TrimSpace(request.Body) == "" {
		return jsonResponse(400, map[string]string{"message": "Empty request body"})
	}

	if request.IsBase64Encoded {
		decodedBody, err := base64.StdEncoding.DecodeString(request.Body)
		if err != nil {
			log.Printf("Error decoding base64 request body: %v", err)
			return jsonResponse(400, map[string]string{"message": "Invalid base64 encoding"})
		}
		request.Body = string(decodedBody)
	}

	var incomingProduct Product
	err := json.Unmarshal([]byte(request.Body), &incomingProduct)
	if err != nil {
		log.Printf("Error parsing request body: %v", err)
		return jsonResponse(400, map[string]string{"message": "Invalid request body"})
	}

	productID, ok := request.PathParameters["id"]
	if !ok || strings.TrimSpace(productID) == "" {
		return jsonResponse(400, map[string]string{"message": "Product ID is required"})
	}

	region := os.Getenv("AWS_REGION")
	if region == "" {
		region = "us-west-2"
	}
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion(region))
	if err != nil {
		log.Printf("Error loading AWS configuration: %v", err)
		return jsonResponse(500, map[string]string{"message": "Internal server error"})
	}
	svc := dynamodb.NewFromConfig(cfg)

	// If incomingProduct.Title is empty, query by ID to retrieve the existing product's title.
	if incomingProduct.Title == "" {
		queryInput := &dynamodb.QueryInput{
			TableName:              aws.String(os.Getenv("TABLE_NAME")),
			KeyConditionExpression: aws.String("ID = :id"),
			ExpressionAttributeValues: map[string]types.AttributeValue{
				":id": &types.AttributeValueMemberS{Value: productID},
			},
			Limit: aws.Int32(1),
		}
		queryResult, err := svc.Query(ctx, queryInput)
		if err != nil || len(queryResult.Items) == 0 {
			log.Printf("Error querying product: %v", err)
			return jsonResponse(500, map[string]string{"message": "Failed to retrieve existing product"})
		}
		var existingProduct Product
		err = attributevalue.UnmarshalMap(queryResult.Items[0], &existingProduct)
		if err != nil {
			log.Printf("Error unmarshalling product query result: %v", err)
			return jsonResponse(500, map[string]string{"message": "Failed to process existing product"})
		}

		// Ensure that the retrieved title is not empty.
		if strings.TrimSpace(existingProduct.Title) == "" {
			log.Printf("Existing product has an empty title")
			return jsonResponse(500, map[string]string{"message": "Existing product is missing a title"})
		}
		incomingProduct.Title = existingProduct.Title
	}

	getItemInput := &dynamodb.GetItemInput{
		TableName: aws.String(os.Getenv("TABLE_NAME")),
		Key: map[string]types.AttributeValue{
			"ID":    &types.AttributeValueMemberS{Value: productID},
			"Title": &types.AttributeValueMemberS{Value: incomingProduct.Title},
		},
	}

	getItemOutput, err := svc.GetItem(ctx, getItemInput)
	if err != nil {
		log.Printf("Error retrieving product from DynamoDB: %v", err)
		return jsonResponse(500, map[string]string{"message": "Failed to retrieve existing product"})
	}
	if getItemOutput.Item == nil {
		return jsonResponse(404, map[string]string{"message": "Product not found"})
	}

	var existingProduct Product
	err = attributevalue.UnmarshalMap(getItemOutput.Item, &existingProduct)
	if err != nil {
		log.Printf("Error unmarshalling existing product: %v", err)
		return jsonResponse(500, map[string]string{"message": "Failed to process existing product"})
	}

	incomingProduct.Title = existingProduct.Title

	updateFields := []string{}
	expressionAttributeValues := make(map[string]types.AttributeValue)
	expressionAttributeNames := make(map[string]string)

	if incomingProduct.Description != "" {
		updateFields = append(updateFields, "#description = :description")
		expressionAttributeValues[":description"] = &types.AttributeValueMemberS{Value: incomingProduct.Description}
		expressionAttributeNames["#description"] = "Description"
	}
	if incomingProduct.Sizes != nil && len(incomingProduct.Sizes) > 0 {
		sizesAttr, err := attributevalue.Marshal(incomingProduct.Sizes)
		if err != nil {
			log.Printf("Error marshaling sizes: %v", err)
			return jsonResponse(500, map[string]string{"message": "Failed to process sizes"})
		}
		updateFields = append(updateFields, "#sizes = :sizes")
		expressionAttributeValues[":sizes"] = sizesAttr
		expressionAttributeNames["#sizes"] = "Sizes"
	}
	if incomingProduct.Prices != nil && len(incomingProduct.Prices) > 0 {
		pricesAttr, err := attributevalue.Marshal(incomingProduct.Prices)
		if err != nil {
			log.Printf("Error marshaling prices: %v", err)
			return jsonResponse(500, map[string]string{"message": "Failed to process prices"})
		}
		updateFields = append(updateFields, "#prices = :prices")
		expressionAttributeValues[":prices"] = pricesAttr
		expressionAttributeNames["#prices"] = "Prices"
	}
	if incomingProduct.ImageUrl != "" {
		updateFields = append(updateFields, "#imageUrl = :imageUrl")
		expressionAttributeValues[":imageUrl"] = &types.AttributeValueMemberS{Value: incomingProduct.ImageUrl}
		expressionAttributeNames["#imageUrl"] = "ImageUrl"
	}
	if incomingProduct.Category != "" {
		updateFields = append(updateFields, "#category = :category")
		expressionAttributeValues[":category"] = &types.AttributeValueMemberS{Value: incomingProduct.Category}
		expressionAttributeNames["#category"] = "Category"
	}
	if incomingProduct.Subcategories != nil && len(incomingProduct.Subcategories) > 0 {
		subcategoriesAttr, err := attributevalue.Marshal(incomingProduct.Subcategories)
		if err != nil {
			log.Printf("Error marshaling subcategories: %v", err)
			return jsonResponse(500, map[string]string{"message": "Failed to process subcategories"})
		}
		updateFields = append(updateFields, "#subcategories = :subcategories")
		expressionAttributeValues[":subcategories"] = subcategoriesAttr
		expressionAttributeNames["#subcategories"] = "Subcategories"
	}
	if incomingProduct.Sold {
		updateFields = append(updateFields, "#sold = :sold")
		expressionAttributeValues[":sold"] = &types.AttributeValueMemberBOOL{Value: incomingProduct.Sold}
		expressionAttributeNames["#sold"] = "Sold"
	}
	if len(updateFields) == 0 {
		return jsonResponse(400, map[string]string{"message": "No fields to update"})
	}
	updateExpression := "SET " + join(updateFields, ", ")

	input := &dynamodb.UpdateItemInput{
		TableName: aws.String(os.Getenv("TABLE_NAME")),
		Key: map[string]types.AttributeValue{
			"ID":    &types.AttributeValueMemberS{Value: productID},
			"Title": &types.AttributeValueMemberS{Value: existingProduct.Title},
		},
		UpdateExpression:          aws.String(updateExpression),
		ExpressionAttributeValues: expressionAttributeValues,
		ExpressionAttributeNames:  expressionAttributeNames,
		ReturnValues:              types.ReturnValueUpdatedNew,
	}

	result, err := svc.UpdateItem(ctx, input)
	if err != nil {
		log.Printf("Error updating item in DynamoDB: %v", err)
		return jsonResponse(500, map[string]string{"message": fmt.Sprintf("Error updating product: %v", err)})
	}

	var updatedProduct Product
	err = attributevalue.UnmarshalMap(result.Attributes, &updatedProduct)
	if err != nil {
		log.Printf("Error unmarshalling updated attributes: %v", err)
		return jsonResponse(500, map[string]string{"message": "Failed to process updated product"})
	}

	return jsonResponse(200, updatedProduct)
}

// jsonResponse creates the HTTP response with CORS headers
func jsonResponse(status int, data interface{}) (events.APIGatewayProxyResponse, error) {
	body, err := json.Marshal(data)
	if err != nil {
		log.Printf("Error marshaling response: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       "Failed to create response",
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: status,
		Body:       string(body),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
			"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(AdminEditProductHandleRequest)
}
