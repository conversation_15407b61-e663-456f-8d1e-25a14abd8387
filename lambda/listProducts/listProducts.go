package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	stripe "github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/price"
	"github.com/stripe/stripe-go/v75/product"
)

var stripeSecretKey = os.Getenv("STRIPE_SECRET_KEY")

func init() {
	if stripeSecretKey == "" {
		panic("STRIPE_SECRET_KEY is not set")
	}
	stripe.Key = stripeSecretKey
}

type PriceDetail struct {
	ID       string `json:"id"`
	Amount   int64  `json:"amount"`
	Currency string `json:"currency"`
}

type ProductResponse struct {
	ID          string        `json:"id"`
	Name        string        `json:"title"`
	Image       string        `json:"image"` // Assuming you have an image URL to return
	Prices      []PriceDetail `json:"prices"`
	Description string        `json:"description"`
}

func ListProductHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {

	// Create a parameters object for listing products, filtering only active products
	params := &stripe.ProductListParams{}
	params.Filters.AddFilter("active", "", "true")

	// Fetch the list of products using the parameters set above
	iter := product.List(params)

	// Define a slice to store the response products
	var products []ProductResponse
	// Iterate through each product returned by the Stripe API
	for iter.Next() {
		p := iter.Product()

		// Default to a placeholder image if the product has no associated images
		var imageUrl string
		if len(p.Images) > 0 {
			imageUrl = p.Images[0]
		} else {
			imageUrl = "URL_FOR_DEFAULT_IMAGE"
		}

		// Create a parameters object to fetch prices associated with the current product
		priceParams := &stripe.PriceListParams{
			Product: &p.ID,
		}
		// Fetch the list of prices using the parameters set above
		priceIter := price.List(priceParams)

		// Define a slice to store the price details for the current product
		var priceDetails []PriceDetail
		// Iterate through each price returned by the Stripe API for the current product
		for priceIter.Next() {
			pr := priceIter.Price()
			// Construct a PriceDetail struct from the Stripe price object
			priceDetail := PriceDetail{
				ID:       pr.ID,
				Amount:   pr.UnitAmount,
				Currency: string(pr.Currency),
			}
			// Append the price detail to the slice
			priceDetails = append(priceDetails, priceDetail)
		}

		// Construct a ProductResponse struct from the Stripe product and associated prices
		productResponse := ProductResponse{
			ID:          p.ID,
			Name:        p.Name,
			Image:       imageUrl,
			Description: p.Description,
			Prices:      priceDetails,
		}
		// Append the product response to the final product list
		products = append(products, productResponse)
	}

	// Convert the product list to JSON for the response body
	responseBody, err := json.Marshal(products)
	if err != nil {
		fmt.Println("Error marshaling response:", err)
		return events.APIGatewayProxyResponse{StatusCode: 500}, err
	}

	// origin := request.Headers["*"]
	// Return the response body with a 200 status code
	return events.APIGatewayProxyResponse{
		Body:       string(responseBody),
		StatusCode: 200,
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "https://thealpinestudio.com",
			"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
			"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {

	// Load environment variables from .env file
	//  err := godotenv.Load()
	//  if err != nil {
	// 	 fmt.Println("Error loading .env file:", err)
	// 	 return
	//  }

	lambda.Start(ListProductHandler)
}
