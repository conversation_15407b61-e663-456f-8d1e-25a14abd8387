package main

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/base64"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

var (
    clientID     = os.Getenv("CLIENT_ID")
    clientSecret = os.Getenv("CLIENT_SECRET")
    region       = os.Getenv("AWS_REGION")
)

type LoginRequest struct {
    Email    string `json:"email"`
    Password string `json:"password"`
}

type LoginResponse struct {
    Message       string `json:"message"`
    ChallengeName string `json:"challenge_name,omitempty"`
    Session       string `json:"session,omitempty"`
    AccessToken   string `json:"access_token,omitempty"`
    IdToken       string `json:"id_token,omitempty"`
    RefreshToken  string `json:"refresh_token,omitempty"`
}

func generateSecretHash(username, clientID, clientSecret string) string {
    message := username + clientID
    h := hmac.New(sha256.New, []byte(clientSecret))
    h.Write([]byte(message))
    return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func loginHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Printf("Received login request: %s", request.Body)

    var loginRequest LoginRequest
    err := json.Unmarshal([]byte(request.Body), &loginRequest)
    if err != nil {
        log.Printf("Error unmarshalling request body: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusBadRequest,
            Body:       fmt.Sprintf(`{"message": "Invalid request: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       fmt.Sprintf(`{"message": "Failed to create AWS session: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    svc := cognitoidentityprovider.New(sess)

    secretHash := generateSecretHash(loginRequest.Email, clientID, clientSecret)
    authInput := &cognitoidentityprovider.InitiateAuthInput{
        AuthFlow: aws.String("USER_PASSWORD_AUTH"),
        AuthParameters: map[string]*string{
            "USERNAME":    aws.String(loginRequest.Email),
            "PASSWORD":    aws.String(loginRequest.Password),
            "SECRET_HASH": aws.String(secretHash),
        },
        ClientId: aws.String(clientID),
    }

    authResult, err := svc.InitiateAuth(authInput)
    if err != nil {
        log.Printf("Error initiating auth: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       fmt.Sprintf(`{"message": "Error initiating auth: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    log.Printf("Auth result: %+v", authResult)

    // Check if MFA is required
    if authResult.ChallengeName != nil && *authResult.ChallengeName == "SOFTWARE_TOKEN_MFA" {
        log.Println("MFA required, session:", *authResult.Session)
        response := LoginResponse{
            Message:       "MFA required",
            ChallengeName: *authResult.ChallengeName,
            Session:       *authResult.Session, // Return session
        }
        responseBody, _ := json.Marshal(response)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusOK,
            Body:       string(responseBody),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    // If MFA is not required, return tokens
    if authResult.AuthenticationResult != nil && authResult.AuthenticationResult.AccessToken != nil {
        log.Println("Login successful, access token:", *authResult.AuthenticationResult.AccessToken)
        response := LoginResponse{
            Message:      "Login successful",
            AccessToken:  *authResult.AuthenticationResult.AccessToken,
            IdToken:      *authResult.AuthenticationResult.IdToken,
            RefreshToken: *authResult.AuthenticationResult.RefreshToken,
        }
        responseBody, _ := json.Marshal(response)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusOK,
            Body:       string(responseBody),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: http.StatusUnauthorized,
        Body:       `{"message": "Login failed"}`,
        Headers: map[string]string{
            "Access-Control-Allow-Origin":      "*",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

func main() {
    lambda.Start(loginHandler)
}
