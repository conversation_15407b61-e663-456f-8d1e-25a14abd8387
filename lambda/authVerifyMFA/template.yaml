AWSTemplateFormatVersion: '2010-09-09'
Transform: AWS::Serverless-2016-10-31
Description: An AWS Lambda  function for Verify Authentication Response.
Resources:
  verifyMfaHanlder:
    Type: AWS::Serverless::Function
    Properties:
      Handler: auth-verify-mfa.lambda_handler
      Runtime: go1.x
      CodeUri: .
      MemorySize: 256
      Timeout: 10
      Environment:
        Variables:
          CLIENT_ID: "6ej9p4miap6snoncgrj3kjtme"
          CLIENT_SECRET: "10nkkdu898itqfjhg55oo86ksbe62abjemcci70av83p6afhg0kr"
      Events:
        Api:
          Type: Api
          Properties:
            Path: /verify-mfa
            Method: post


