package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

var (
	clientID     = os.Getenv("CLIENT_ID")
	clientSecret = os.Getenv("CLIENT_SECRET")
	region       = os.Getenv("AWS_REGION")
)

type VerifyMFARequest struct {
	Username string `json:"username"`
	Session  string `json:"session"`
	MFA      string `json:"mfa_code"`
}

type VerifyMFAResponse struct {
	Message      string `json:"message"`
	AccessToken        string `json:"access_token,omitempty"`
	IdToken      string `json:"id_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
}

func generateSecretHash(username, clientID, clientSecret string) string {
	message := username + clientID
	h := hmac.New(sha256.New, []byte(clientSecret))
	h.Write([]byte(message))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func verifyMFAHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Println("Verify MFA Handler invoked")

	var verifyRequest VerifyMFARequest
	err := json.Unmarshal([]byte(request.Body), &verifyRequest)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       fmt.Sprintf(`{"message": "Invalid request: %v"}`, err),
		}, nil
	}

	log.Printf("Parsed request: Username=%s, Session=%s, MFA Code=%s", verifyRequest.Username, verifyRequest.Session, verifyRequest.MFA)

	if len(verifyRequest.Session) == 0 {
		fmt.Println("Invalid session token length", len(verifyRequest.Session))
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       `{"message": "Invalid session token"}`,
		}, nil
	}

	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Failed to create AWS session: %v"}`, err),
		}, nil
	}

	svc := cognitoidentityprovider.New(sess)

	// Generate secret hash
	secretHash := generateSecretHash(verifyRequest.Username, clientID, clientSecret)

	// Respond to MFA challenge
	input := &cognitoidentityprovider.RespondToAuthChallengeInput{
		ChallengeName: aws.String("SOFTWARE_TOKEN_MFA"),
		ClientId:      aws.String(clientID),
		Session:       aws.String(verifyRequest.Session), // Use the session here
		ChallengeResponses: map[string]*string{
			"USERNAME":                aws.String(verifyRequest.Username),
			"SOFTWARE_TOKEN_MFA_CODE": aws.String(verifyRequest.MFA),
			"SECRET_HASH":             aws.String(secretHash),
		},
	}

	result, err := svc.RespondToAuthChallenge(input)
	if err != nil {
		fmt.Printf("Error verifying MFA: %v\n", err) // Add logging
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusUnauthorized,
			Body:       fmt.Sprintf(`{"message": "MFA verification failed: %v"}`, err),
		}, nil
	}

	response := VerifyMFAResponse{
		Message: "MFA verification successful",
	}
	if result.AuthenticationResult != nil {
		if result.AuthenticationResult.AccessToken != nil {
			response.AccessToken = *result.AuthenticationResult.AccessToken
		}
		if result.AuthenticationResult.IdToken != nil {
			response.IdToken = *result.AuthenticationResult.IdToken
		}
		if result.AuthenticationResult.RefreshToken != nil {
			response.RefreshToken = *result.AuthenticationResult.RefreshToken
		}
	}

	fmt.Printf("Refresh token: %s\n", response.RefreshToken)

	responseBody, err := json.Marshal(response)
	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Error marshalling response: %v"}`, err),
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(verifyMFAHandler)
}
