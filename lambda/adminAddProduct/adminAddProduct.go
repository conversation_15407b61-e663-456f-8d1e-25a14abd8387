package main

import (
    "bytes"
    "encoding/base64"
    "encoding/json"
    "fmt"
    "net/http"
    "os"
    "strings"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
    "github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
    "github.com/aws/aws-sdk-go/service/s3"
)

// Price represents the price structure with currency and amount.
type Price struct {
    Amount   float64 `json:"amount" dynamodbav:"Amount"`
    Currency string  `json:"currency" dynamodbav:"Currency"`
}

// Size represents the size and price of a product.
type Size struct {
    Size  string `json:"size" dynamodbav:"Size"`
    Price Price  `json:"price" dynamodbav:"Price"`
}

// Product represents the structure of a product to be stored in DynamoDB.
type Product struct {
    ID            string   `json:"id" dynamodbav:"ID"`
    Title         string   `json:"title" dynamodbav:"Title"`
    Description   string   `json:"description" dynamodbav:"Description"`
    Category      string   `json:"category" dynamodbav:"category"`
    Subcategories []string `json:"subcategories" dynamodbav:"subcategories"`
    Sizes         []Size   `json:"sizes" dynamodbav:"Sizes"`
    ImageUrl      string   `json:"imageUrl" dynamodbav:"ImageUrl"`
    Sold          bool     `json:"sold" dynamodbav:"sold"`
}

// Response represents the structure of the API response.
type Response struct {
    Message   string `json:"message"`
    ProductID string `json:"productId,omitempty"`
}

// handleUpload processes the Lambda event and stores a product in DynamoDB.
func handleUpload(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    fmt.Printf("Received event: %+v\n", request)
    fmt.Printf("HTTP Method: %s\n", request.HTTPMethod)

    // 1) Parse incoming JSON
    var product Product
    if request.IsBase64Encoded {
        decodedBody, err := base64.StdEncoding.DecodeString(request.Body)
        if err != nil {
            return corsJSONResponse(http.StatusBadRequest, "Failed to decode request body: "+err.Error(), "")
        }
        request.Body = string(decodedBody)
    }
    if err := json.Unmarshal([]byte(request.Body), &product); err != nil {
        return corsJSONResponse(http.StatusBadRequest, "Failed to parse request body: "+err.Error(), "")
    }

    // 2) Validate required fields
    if product.Title == "" {
        return corsJSONResponse(http.StatusBadRequest, "Title is required", "")
    }
    if product.Category == "" {
        return corsJSONResponse(http.StatusBadRequest, "Category is required", "")
    }

    // Some categories require at least one subcategory
    categoriesRequiringSubcat := map[string]bool{
        "Apparel":                          true,
        "Accessories":                      true,
        "Open Edition Prints and Stickers": true,
        "Originals":                        true,
    }
    if requiresSubcategory(product.Category, categoriesRequiringSubcat) {
        if len(product.Subcategories) == 0 {
            msg := fmt.Sprintf("At least one subcategory is required for category '%s'", product.Category)
            return corsJSONResponse(http.StatusBadRequest, msg, "")
        }
    }

    // 3) Generate a unique product ID
    productID := fmt.Sprintf("%d", time.Now().Unix())
    product.ID = productID
    fmt.Println("Generated Product ID:", productID)
    fmt.Printf("Parsed product struct: %+v\n", product)

    // 4) Initialize AWS clients
    sess := session.Must(session.NewSession(&aws.Config{
        Region: aws.String("us-west-2"),
    }))
    dynamoClient := dynamodb.New(sess)
    s3Client := s3.New(sess)

    // 5) Check environment variables
    imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
    if imageBucketName == "" {
        return corsJSONResponse(http.StatusInternalServerError, "IMAGE_BUCKET_NAME not set", "")
    }
    tableName := os.Getenv("TABLE_NAME")
    if tableName == "" {
        return corsJSONResponse(http.StatusInternalServerError, "TABLE_NAME not set", "")
    }

    // 6) If the product's ImageUrl is a data URI, handle base64 image upload
    if strings.HasPrefix(product.ImageUrl, "data:image/") {
        parts := strings.Split(product.ImageUrl, ",")
        if len(parts) != 2 {
            return corsJSONResponse(http.StatusBadRequest, "Invalid image data format", "")
        }
        imageData := parts[1]
        decodedImage, err := base64.StdEncoding.DecodeString(imageData)
        if err != nil {
            return corsJSONResponse(http.StatusBadRequest, "Invalid image data: "+err.Error(), "")
        }
        // Optionally check size (avoid > 10MB):
        if len(decodedImage) > 10*1024*1024 {
            return corsJSONResponse(http.StatusRequestEntityTooLarge, "Image data is too large (max 10MB). Use presigned URL instead.", "")
        }

        imageFormat := strings.TrimPrefix(strings.Split(parts[0], ";")[0], "data:image/")
        imageKey := fmt.Sprintf("originals/%s.%s", productID, imageFormat)

        // Upload to S3
        _, err = s3Client.PutObject(&s3.PutObjectInput{
            Bucket:      aws.String(imageBucketName),
            Key:         aws.String(imageKey),
            Body:        bytes.NewReader(decodedImage),
            ContentType: aws.String("image/" + imageFormat),
        })
        if err != nil {
            return corsJSONResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to upload image to S3: %v", err), "")
        }
        fmt.Printf("Image uploaded successfully: s3://%s/%s\n", imageBucketName, imageKey)

        // Set final public URL (assuming your bucket allows GET or a CloudFront distribution)
        product.ImageUrl = fmt.Sprintf("https://%s.s3.amazonaws.com/%s", imageBucketName, imageKey)
    }

    // 7) Save to DynamoDB
    item, err := dynamodbattribute.MarshalMap(product)
    if err != nil {
        return corsJSONResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to marshal product: %v", err), "")
    }
    fmt.Printf("DynamoDB item: %+v\n", item)

    _, err = dynamoClient.PutItem(&dynamodb.PutItemInput{
        TableName: aws.String(tableName),
        Item:      item,
    })
    if err != nil {
        return corsJSONResponse(http.StatusInternalServerError, fmt.Sprintf("Failed to save product metadata: %v", err), "")
    }

    // 8) Return success
    successMsg := fmt.Sprintf("Product %s created successfully", product.ID)
    return corsJSONResponse(http.StatusCreated, successMsg, product.ID)
}

// corsJSONResponse consistently returns JSON plus CORS headers
func corsJSONResponse(status int, message, productID string) (events.APIGatewayProxyResponse, error) {
    responseBody := Response{
        Message:   message,
        ProductID: productID,
    }
    body, err := json.Marshal(responseBody)
    if err != nil {
        // If JSON marshalling fails, fallback
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       "Failed to create response",
            Headers:    defaultCORSHeaders(),
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: status,
        Body:       string(body),
        Headers:    defaultCORSHeaders(),
    }, nil
}

// defaultCORSHeaders returns the standard headers for CORS
func defaultCORSHeaders() map[string]string {
    return map[string]string{
        "Content-Type":                     "application/json",
        "Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com", // or "*"
        "Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
        "Access-Control-Allow-Headers":     "Content-Type,Authorization",
        "Access-Control-Allow-Credentials": "true",
    }
}

// requiresSubcategory checks if a category is in the map that requires subcategories
func requiresSubcategory(category string, categoriesMap map[string]bool) bool {
    return categoriesMap[category]
}

func main() {
    lambda.Start(handleUpload)
}
