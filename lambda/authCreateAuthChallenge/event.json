{
    "version": "1",
    "triggerSource": "PreAuthentication_Authentication",
    "region": "us-west-2",
    "userPoolId": "us-west-2_YXmFG33l7",
    "userName": "testuser",
    "callerContext": {
      "awsSdkVersion": "aws-sdk-unknown-unknown",
      "clientId": "1harpikirsevqpo08q1g5jff8d"
    },
    "request": {
      "userAttributes": {
        "email": "<EMAIL>",
        "phone_number": "+17325985266",
        "phone_number_verified": "true"
      }
    },
    "response": {}
  }
  
  {
    "version": "1",
    "triggerSource": "CreateAuthChallenge_Authentication",
    "region": "us-west-2",
    "userPoolId": "us-west-2_YXmFG33l7",
    "userName": "<EMAIL>",
    "callerContext": {
      "awsSdkVersion": "aws-sdk-unknown-unknown",
      "clientId": "1harpikirsevqpo08q1g5jff8d"
    },
    "request": {
      "userAttributes": {
        "sub": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
        "email_verified": "true",
        "email": "<EMAIL>"
      },
      "session": [
        {
          "challengeName": "SRP_A",
          "challengeResult": true,
          "challengeMetadata": "string"
        }
      ]
    },
    "response": {
      "publicChallengeParameters": {},
      "privateChallengeParameters": {},
      "challengeMetadata": "string"
    }
  }
  