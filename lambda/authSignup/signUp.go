package main

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"regexp"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

var (
	clientID     = os.Getenv("CLIENT_ID")
	clientSecret = os.Getenv("CLIENT_SECRET")
	region       = os.Getenv("AWS_REGION")
)

type SignUpRequest struct {
	Email       string `json:"email"`
	Password    string `json:"password"`
	Username        string `json:"username"`
	PhoneNumber string `json:"phone_number"`
}

type CognitoSignUpRequest struct {
	ClientId       string              `json:"ClientId"`
	SecretHash     string              `json:"SecretHash"`
	Username       string              `json:"Username"`
	Password       string              `json:"Password"`
	UserAttributes []map[string]string `json:"UserAttributes"`
}

func generateSecretHash(username, clientID, clientSecret string) string {
	message := username + clientID
	h := hmac.New(sha256.New, []byte(clientSecret))
	h.Write([]byte(message))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func validateInput(signUpRequest SignUpRequest) error {
	if len(signUpRequest.Email) == 0 {
		return fmt.Errorf("email cannot be empty")
	}
	if len(signUpRequest.Password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}
	re := regexp.MustCompile(`^[\S]+.*[\S]+$`)
	if !re.MatchString(signUpRequest.Password) {
		return fmt.Errorf("password must satisfy regular expression pattern: ^[\\S]+.*[\\S]+$")
	}
	if len(signUpRequest.Username) == 0 {
		return fmt.Errorf("Username cannot be empty")
	}
	return nil
}

func signUpHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// log.Println("Full Request:", request)
	// log.Println("Request Body:", request.Body)
	// log.Println("Request Headers:", request.Headers)

	if request.Body == "" {
		log.Println("Error: Request body is empty")
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       `{"message": "Request body is empty"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

    // Parse the request body
	var signUpRequest SignUpRequest
	err := json.Unmarshal([]byte(request.Body), &signUpRequest)
	if err != nil {
		// log.Println("Error unmarshalling request body:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       `{"message": "Invalid request body"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}

	// log.Println("Parsed sign up request:", signUpRequest)
	// log.Printf("Email: %s\n", signUpRequest.Email)
	// log.Printf("Password: %s\n", signUpRequest.Password)
	// log.Printf("Name: %s\n", signUpRequest.Name)
	// log.Printf("Phone Number: %s\n", signUpRequest.PhoneNumber)

	// Validate input
	err = validateInput(signUpRequest)
	if err != nil {
		// log.Println("Input validation error:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       fmt.Sprintf(`{"message": "%s"}`, err.Error()),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	secretHash := generateSecretHash(signUpRequest.Email, clientID, clientSecret)

	cognitoSignUpRequest := CognitoSignUpRequest{
		ClientId:   clientID,
		SecretHash: secretHash,
		Username:   signUpRequest.Email,
		Password:   signUpRequest.Password,
		UserAttributes: []map[string]string{
			{"Name": "email", "Value": signUpRequest.Email},
			{"Name": "name", "Value": signUpRequest.Username},
			{"Name": "phone_number", "Value": signUpRequest.PhoneNumber},
		},
	}

	log.Printf("Cognito Sign Up Request: %+v\n", cognitoSignUpRequest)

	jsonBody, err := json.Marshal(cognitoSignUpRequest)
	if err != nil {
		log.Println("Error marshalling Cognito sign-up request:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       `{"message": "Error marshalling request"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}

	req, err := http.NewRequest("POST", fmt.Sprintf("https://cognito-idp.%s.amazonaws.com/", region), bytes.NewBuffer(jsonBody))
	if err != nil {
		log.Println("Error creating HTTP request:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       `{"message": "Error creating request"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}

	req.Header.Set("Content-Type", "application/x-amz-json-1.1")
	req.Header.Set("X-Amz-Target", "AWSCognitoIdentityProviderService.SignUp")
	req.Header.Set("X-Amz-User-Agent", "AWS-SDK-JS/1.0.0")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Println("Error making HTTP request:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       `{"message": "Error making request"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}
	defer resp.Body.Close()

	var result map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&result)
	if err != nil {
		log.Println("Error parsing response body:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       `{"message": "Error parsing response"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}

	log.Println("Cognito response status code:", resp.StatusCode)
	log.Println("Cognito response:", result)

	if resp.StatusCode != http.StatusOK {
		log.Println("Error response from Cognito:", result)
		return events.APIGatewayProxyResponse{
			StatusCode: resp.StatusCode,
			Body:       fmt.Sprintf(`{"message": "%s"}`, result["message"].(string)),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	log.Println("Sign up successful:", result)
	responseBody, err := json.Marshal(map[string]string{"message": "Sign up successful. Please check your email for confirmation."})
	if err != nil {
		log.Println("Error marshalling response body:", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       `{"message": "Error marshalling response"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, err
	}

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(signUpHandler)
}
