package main

import (
    "context"
    "crypto/hmac"
    "crypto/sha256"
    "encoding/base64"
    "encoding/json"
    "log"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

type AuthRequest struct {
    Username    string `json:"username"`
    Password    string `json:"password"`
}

type AuthResponse struct {
    Message string `json:"message"`
    Token   string `json:"token,omitempty"`
}

var (
    client_id     = os.Getenv("CLIENT_ID")
    client_secret = os.Getenv("CLIENT_SECRET")
)

func handleForgotPassword(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Println("Request method:", request.HTTPMethod)

    if request.HTTPMethod == "OPTIONS" {
        return events.APIGatewayProxyResponse{Headers: addCORSHeaders(), StatusCode: 200}, nil
    }

    var req AuthRequest
    if err := json.Unmarshal([]byte(request.Body), &req); err != nil {
        return responseWithError("Invalid request body", 400)
    }

    sess, err := session.NewSession(&aws.Config{Region: aws.String("us-west-2")})
    if err != nil {
        return responseWithError("Failed to create AWS session", 500)
    }

    svc := cognitoidentityprovider.New(sess)
    authInput := &cognitoidentityprovider.InitiateAuthInput{
        AuthFlow: aws.String("USER_PASSWORD_AUTH"),
        AuthParameters: map[string]*string{
            "USERNAME":    aws.String(req.Username),
            "PASSWORD":    aws.String(req.Password),
            "SECRET_HASH": aws.String(GenerateSecretHash(req.Username)),
        },
        ClientId: aws.String(client_id),
    }

    result, err := svc.InitiateAuth(authInput)
    if err != nil {
        return responseWithError("Authentication failed: "+err.Error(), 401)
    }

    if result.ChallengeName != nil {
        switch *result.ChallengeName {
        case "NEW_PASSWORD_REQUIRED":
            return events.APIGatewayProxyResponse{
                Headers:    addCORSHeaders(),
                StatusCode: 400,
                Body:       jsonMustMarshal(map[string]string{"message": "New password required but not provided."}),
            }, nil
        }
    }

    if result.AuthenticationResult != nil && result.AuthenticationResult.AccessToken != nil {
        return successfulAuthResponse(*result.AuthenticationResult.AccessToken)
    }

    return responseWithError("Unhandled authentication case", 500)
}

func GenerateSecretHash(username string) string {
    secret := client_secret              // Use the actual client secret from environment variable
    data := []byte(username + client_id) // Data typically includes the username and client ID
    h := hmac.New(sha256.New, []byte(secret))
    h.Write(data)
    hash := base64.StdEncoding.EncodeToString(h.Sum(nil))
    log.Printf("Generated Secret Hash for %s: %s\n", username, hash)
    return hash
}

func addCORSHeaders() map[string]string {
    return map[string]string{
        "Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
        "Access-Control-Allow-Methods":     "POST, OPTIONS",
        "Access-Control-Allow-Headers":     "Content-Type, Authorization",
        "Access-Control-Allow-Credentials": "true",
    }
}

func responseWithError(message string, statusCode int) (events.APIGatewayProxyResponse, error) {
    return events.APIGatewayProxyResponse{
        Body:       jsonMustMarshal(map[string]string{"message": message}),
        StatusCode: statusCode,
        Headers:    addCORSHeaders(),
    }, nil
}

func successfulAuthResponse(token string) (events.APIGatewayProxyResponse, error) {
    return events.APIGatewayProxyResponse{
        Body:       jsonMustMarshal(AuthResponse{Message: "Authentication successful", Token: token}),
        StatusCode: 200,
        Headers:    addCORSHeaders(),
    }, nil
}

func jsonMustMarshal(data interface{}) string {
    jsonBytes, err := json.Marshal(data)
    if err != nil {
        log.Fatalf("JSON marshalling failed: %s", err)
    }
    return string(jsonBytes)
}

func main() {
    lambda.Start(handleForgotPassword)
}
